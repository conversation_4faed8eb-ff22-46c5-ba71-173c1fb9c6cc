{"$ref": "#/definitions/blog", "definitions": {"blog": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "publishDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "string", "format": "date"}, {"type": "integer", "format": "unix-time"}]}, "updatedDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "string", "format": "date"}, {"type": "integer", "format": "unix-time"}]}, "author": {"type": "string", "default": "Gun<PERSON>"}, "image": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}, "default": []}, "category": {"type": "string"}, "featured": {"type": "boolean", "default": false}, "draft": {"type": "boolean", "default": false}, "$schema": {"type": "string"}}, "required": ["title", "description", "publishDate"], "additionalProperties": false}}, "$schema": "http://json-schema.org/draft-07/schema#"}