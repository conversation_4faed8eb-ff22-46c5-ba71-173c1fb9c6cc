{"$ref": "#/definitions/projects", "definitions": {"projects": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "image": {"type": "string"}, "gallery": {"type": "array", "items": {"type": "string"}}, "technologies": {"type": "array", "items": {"type": "string"}}, "liveUrl": {"type": "string", "format": "uri"}, "githubUrl": {"type": "string", "format": "uri"}, "featured": {"type": "boolean", "default": false}, "publishDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "string", "format": "date"}, {"type": "integer", "format": "unix-time"}]}, "status": {"type": "string", "enum": ["completed", "in-progress", "planned"], "default": "completed"}, "$schema": {"type": "string"}}, "required": ["title", "description", "technologies", "publishDate"], "additionalProperties": false}}, "$schema": "http://json-schema.org/draft-07/schema#"}