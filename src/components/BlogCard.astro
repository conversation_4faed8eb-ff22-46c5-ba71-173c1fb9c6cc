---
export interface Props {
  title: string;
  description: string;
  publishDate: Date;
  image?: string;
  tags: string[];
  slug: string;
  featured?: boolean;
}

const { title, description, publishDate, image, tags, slug, featured } = Astro.props;

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(date);
};
---

<article class={`blog-card ${featured ? 'featured' : ''}`}>
  {image && (
    <div class="blog-image">
      <img src={image} alt={title} loading="lazy" />
    </div>
  )}
  
  <div class="blog-content">
    <div class="blog-meta">
      <time datetime={publishDate.toISOString()}>
        {formatDate(publishDate)}
      </time>
    </div>
    
    <h3 class="blog-title">
      <a href={`/blog/${slug}`}>{title}</a>
    </h3>
    
    <p class="blog-description">{description}</p>
    
    {tags.length > 0 && (
      <div class="blog-tags">
        {tags.map((tag) => (
          <span class="tag">{tag}</span>
        ))}
      </div>
    )}
    
    <a href={`/blog/${slug}`} class="read-more">
      Read More →
    </a>
  </div>
</article>

<style>
  .blog-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .blog-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
  }

  .blog-card.featured {
    border: 2px solid var(--primary-color);
  }

  .blog-image {
    aspect-ratio: 16/9;
    overflow: hidden;
  }

  .blog-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .blog-card:hover .blog-image img {
    transform: scale(1.05);
  }

  .blog-content {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .blog-meta {
    margin-bottom: 0.75rem;
  }

  .blog-meta time {
    color: var(--secondary-color);
    font-size: 0.875rem;
    font-weight: 500;
  }

  .blog-title {
    margin: 0 0 0.75rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    line-height: 1.4;
  }

  .blog-title a {
    color: var(--text-color);
    text-decoration: none;
    transition: color 0.3s ease;
  }

  .blog-title a:hover {
    color: var(--primary-color);
  }

  .blog-description {
    color: var(--secondary-color);
    margin-bottom: 1rem;
    flex: 1;
    line-height: 1.6;
  }

  .blog-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
  }

  .tag {
    background: var(--hover-color);
    color: var(--secondary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .read-more {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    transition: color 0.3s ease;
    align-self: flex-start;
  }

  .read-more:hover {
    color: #1d4ed8;
  }

  @media (max-width: 768px) {
    .blog-content {
      padding: 1rem;
    }
  }
</style>
