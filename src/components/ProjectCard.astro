---
export interface Props {
  title: string;
  description: string;
  image?: string;
  technologies: string[];
  liveUrl?: string;
  githubUrl?: string;
  slug: string;
  featured?: boolean;
}

const { title, description, image, technologies, liveUrl, githubUrl, slug, featured } = Astro.props;
---

<article class={`project-card ${featured ? 'featured' : ''}`}>
  {image && (
    <div class="project-image">
      <img src={image} alt={title} loading="lazy" />
    </div>
  )}
  
  <div class="project-content">
    <h3 class="project-title">
      <a href={`/projects/${slug}`}>{title}</a>
    </h3>
    
    <p class="project-description">{description}</p>
    
    <div class="project-technologies">
      {technologies.map((tech) => (
        <span class="tech-tag">{tech}</span>
      ))}
    </div>
    
    <div class="project-links">
      {liveUrl && (
        <a href={liveUrl} target="_blank" rel="noopener noreferrer" class="project-link live">
          Live Demo
        </a>
      )}
      {githubUrl && (
        <a href={githubUrl} target="_blank" rel="noopener noreferrer" class="project-link github">
          GitHub
        </a>
      )}
      <a href={`/projects/${slug}`} class="project-link details">
        View Details
      </a>
    </div>
  </div>
</article>

<style>
  .project-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .project-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
  }

  .project-card.featured {
    border: 2px solid var(--primary-color);
  }

  .project-image {
    aspect-ratio: 16/9;
    overflow: hidden;
  }

  .project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .project-card:hover .project-image img {
    transform: scale(1.05);
  }

  .project-content {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .project-title {
    margin: 0 0 0.75rem 0;
    font-size: 1.25rem;
    font-weight: 600;
  }

  .project-title a {
    color: var(--text-color);
    text-decoration: none;
    transition: color 0.3s ease;
  }

  .project-title a:hover {
    color: var(--primary-color);
  }

  .project-description {
    color: var(--secondary-color);
    margin-bottom: 1rem;
    flex: 1;
  }

  .project-technologies {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
  }

  .tech-tag {
    background: var(--hover-color);
    color: var(--secondary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .project-links {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
  }

  .project-link {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    text-align: center;
    flex: 1;
    min-width: fit-content;
  }

  .project-link.live {
    background: var(--primary-color);
    color: white;
  }

  .project-link.live:hover {
    background: #1d4ed8;
  }

  .project-link.github {
    background: #24292e;
    color: white;
  }

  .project-link.github:hover {
    background: #1a1e22;
  }

  .project-link.details {
    background: var(--hover-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }

  .project-link.details:hover {
    background: var(--border-color);
  }

  @media (max-width: 768px) {
    .project-links {
      flex-direction: column;
    }
    
    .project-link {
      flex: none;
    }
  }
</style>
