---
title: "Portfolio Website with Astro & Decap CMS"
description: "A modern portfolio website built with Astro framework and integrated with Decap CMS for easy content management."
image: "/images/portfolio-preview.jpg"
technologies: ["Astro", "TypeScript", "Decap CMS", "CSS", "Vercel"]
liveUrl: "https://bagusfarisa-astro.vercel.app"
githubUrl: "https://github.com/bagusfarisa/bagusfarisa-astro"
featured: true
publishDate: 2024-06-05T10:00:00.000Z
status: "completed"
---

# Portfolio Website with Astro & Decap CMS

This portfolio website showcases my projects and blog posts, built with modern web technologies for optimal performance and easy content management.

## Features

- **Static Site Generation**: Built with <PERSON>tro for lightning-fast performance
- **Content Management**: Integrated with Decap CMS for easy content editing
- **Responsive Design**: Mobile-first approach ensuring great experience on all devices
- **SEO Optimized**: Proper meta tags, structured data, and performance optimization
- **Modern Stack**: TypeScript, CSS custom properties, and modern JavaScript

## Technical Implementation

### Astro Framework
The site leverages Astro's unique approach to static site generation, allowing for:
- Zero JavaScript by default
- Component-based architecture
- Content collections for type-safe content management
- Excellent performance out of the box

### Decap CMS Integration
Content management is handled through Decap CMS, providing:
- Git-based workflow
- Rich text editing
- Media management
- Preview functionality
- User-friendly interface for non-technical users

### Deployment
The site is deployed on Vercel with:
- Automatic deployments from Git
- Edge network distribution
- Optimized build process
- Environment variable management

## Development Process

1. **Planning**: Defined the site structure and content requirements
2. **Design**: Created a clean, professional design system
3. **Development**: Built components and pages with Astro
4. **CMS Setup**: Configured Decap CMS for content management
5. **Testing**: Ensured cross-browser compatibility and performance
6. **Deployment**: Set up CI/CD pipeline with Vercel

This project demonstrates my ability to work with modern web technologies while maintaining focus on performance, accessibility, and user experience.
