---
export interface Props {
  title: string;
  description?: string;
  image?: string;
}

const { title, description = "Portfolio website of Guntur - Software Developer", image } = Astro.props;
const canonicalURL = new URL(Astro.url.pathname, Astro.site);
---

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description} />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content={canonicalURL} />
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    {image && <meta property="og:image" content={new URL(image, Astro.url)} />}
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content={canonicalURL} />
    <meta property="twitter:title" content={title} />
    <meta property="twitter:description" content={description} />
    {image && <meta property="twitter:image" content={new URL(image, Astro.url)} />}
    
    <title>{title}</title>
  </head>
  <body>
    <nav class="navbar">
      <div class="nav-container">
        <a href="/" class="nav-logo">Guntur</a>
        <ul class="nav-menu">
          <li class="nav-item">
            <a href="/" class="nav-link">Home</a>
          </li>
          <li class="nav-item">
            <a href="/projects" class="nav-link">Projects</a>
          </li>
          <li class="nav-item">
            <a href="/blog" class="nav-link">Blog</a>
          </li>
        </ul>
      </div>
    </nav>
    
    <main>
      <slot />
    </main>
    
    <footer class="footer">
      <div class="footer-container">
        <p>&copy; 2024 Guntur. All rights reserved.</p>
        <div class="social-links">
          <a href="https://github.com/bagusfarisa" target="_blank" rel="noopener noreferrer">GitHub</a>
          <a href="https://linkedin.com/in/bagusfarisa" target="_blank" rel="noopener noreferrer">LinkedIn</a>
        </div>
      </div>
    </footer>
  </body>
</html>

<style>
  :root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --text-color: #1e293b;
    --bg-color: #ffffff;
    --border-color: #e2e8f0;
    --hover-color: #f8fafc;
  }

  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--bg-color);
  }

  .navbar {
    background: var(--bg-color);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 100;
  }

  .nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .nav-logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
    text-decoration: none;
  }

  .nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
  }

  .nav-link {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
  }

  .nav-link:hover {
    color: var(--primary-color);
  }

  main {
    min-height: calc(100vh - 140px);
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
  }

  .footer {
    background: var(--hover-color);
    border-top: 1px solid var(--border-color);
    margin-top: 4rem;
  }

  .footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .social-links {
    display: flex;
    gap: 1rem;
  }

  .social-links a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: color 0.3s ease;
  }

  .social-links a:hover {
    color: var(--primary-color);
  }

  @media (max-width: 768px) {
    .nav-container {
      padding: 1rem;
    }
    
    .nav-menu {
      gap: 1rem;
    }
    
    main {
      padding: 1rem;
    }
    
    .footer-container {
      flex-direction: column;
      text-align: center;
    }
  }
</style>
