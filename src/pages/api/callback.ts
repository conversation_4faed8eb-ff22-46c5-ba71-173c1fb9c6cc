import type { APIRoute } from 'astro';

const CLIENT_ID = 'Ov23liYrLSc03pEujzBl';
const CLIENT_SECRET = import.meta.env.GITHUB_CLIENT_SECRET;

function renderBody(status: string, content: any) {
  const isSuccess = status === 'success';
  const token = isSuccess ? content.token : '';
  const provider = isSuccess ? content.provider : '';
  const error = !isSuccess ? (content.error || 'Authentication failed') : '';

  const bodyContent = isSuccess
    ? '<div class="spinner"></div><h2 class="success">Authentication Successful!</h2><p>Completing authorization...</p>'
    : '<h2 class="error">Authentication Failed</h2><p>There was an error during authentication.</p>';

  const successScript = isSuccess
    ? `
      // Send the token back to the parent window (Decap CMS)
      e.source.postMessage(
        "authorization:github:success:" + JSON.stringify({
          token: "${token}",
          provider: "${provider}"
        }),
        e.origin
      );`
    : `
      // Send error message to parent
      e.source.postMessage(
        "authorization:github:error:" + JSON.stringify({
          error: "${error}"
        }),
        e.origin
      );`;

  const immediateScript = isSuccess
    ? `
      window.opener.postMessage(
        "authorization:github:success:" + JSON.stringify({
          token: "${token}",
          provider: "${provider}"
        }),
        window.location.origin
      );`
    : `
      window.opener.postMessage(
        "authorization:github:error:" + JSON.stringify({
          error: "${error}"
        }),
        window.location.origin
      );`;

  const html = `<!DOCTYPE html>
<html>
<head>
  <title>Authorizing...</title>
  <meta name="robots" content="noindex">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      margin: 0;
      background: #f5f5f5;
    }
    .container {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      text-align: center;
      max-width: 400px;
    }
    .spinner {
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 2s linear infinite;
      margin: 0 auto 1rem;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .error {
      color: #dc3545;
    }
    .success {
      color: #28a745;
    }
  </style>
</head>
<body>
  <div class="container">
    ${bodyContent}
  </div>

  <script>
    (function() {
      function receiveMessage(e) {
        console.log("receiveMessage %o", e);
        ${successScript}
      }

      window.addEventListener("message", receiveMessage, false);

      // Also try to send the message immediately in case the parent is ready
      if (window.opener) {
        ${immediateScript}
      }
    })();
  </script>
</body>
</html>`;

  return html;
}

export const GET: APIRoute = async ({ url }) => {
  try {
    if (!CLIENT_SECRET) {
      throw new Error('GitHub client secret not configured');
    }

    const code = url.searchParams.get('code');
    
    if (!code) {
      throw new Error('No authorization code received');
    }

    const response = await fetch('https://github.com/login/oauth/access_token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'astro-decap-cms-oauth',
        'Accept': 'application/json',
      },
      body: JSON.stringify({
        client_id: CLIENT_ID,
        client_secret: CLIENT_SECRET,
        code: code,
      }),
    });

    const result = await response.json();

    if (result.error) {
      const errorHtml = renderBody('error', { error: result.error_description || result.error });
      return new Response(errorHtml, {
        headers: {
          'Content-Type': 'text/html; charset=UTF-8',
        },
        status: 401,
      });
    }

    const token = result.access_token;
    const provider = 'github';

    const successHtml = renderBody('success', { token, provider });
    
    return new Response(successHtml, {
      headers: {
        'Content-Type': 'text/html; charset=UTF-8',
      },
      status: 200,
    });

  } catch (error) {
    console.error('Callback error:', error);
    
    const errorHtml = renderBody('error', { 
      error: error instanceof Error ? error.message : 'Authentication failed' 
    });
    
    return new Response(errorHtml, {
      headers: {
        'Content-Type': 'text/html; charset=UTF-8',
      },
      status: 500,
    });
  }
};
