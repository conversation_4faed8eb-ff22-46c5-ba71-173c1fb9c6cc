import type { APIRoute } from 'astro';

export const GET: APIRoute = async ({ url }) => {
  const html = `
<!DOCTYPE html>
<html>
<head>
  <title>Auth Test</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 40px; }
    .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
    .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    button { padding: 10px 20px; margin: 10px 0; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
    button:hover { background: #0056b3; }
  </style>
</head>
<body>
  <h1>GitHub OAuth Test</h1>
  
  <div class="info">
    <strong>Current URL:</strong> ${url.href}
  </div>
  
  <div class="info">
    <strong>Auth Endpoint:</strong> <a href="/auth" target="_blank">/auth</a>
  </div>
  
  <div class="info">
    <strong>Admin Interface:</strong> <a href="/admin/" target="_blank">/admin/</a>
  </div>
  
  <h2>Test OAuth Flow</h2>
  <button onclick="testAuth()">Test GitHub OAuth Popup</button>
  <button onclick="testAuthDirect()">Test Auth Endpoint Directly</button>

  <h2>Configuration Check</h2>
  <div id="config-status">Checking configuration...</div>

  <h2>OAuth Messages</h2>
  <div id="oauth-messages"></div>

  <script>
    let popup = null;

    function testAuth() {
      if (popup && !popup.closed) {
        popup.close();
      }

      popup = window.open('/auth', 'oauth-test', 'width=600,height=600,scrollbars=yes,resizable=yes');

      // Listen for messages from the popup
      window.addEventListener('message', function(event) {
        console.log('Received message:', event);

        const messagesDiv = document.getElementById('oauth-messages');
        const messageElement = document.createElement('div');
        messageElement.className = 'info';
        messageElement.innerHTML = '<strong>Message received:</strong> ' + JSON.stringify(event.data);
        messagesDiv.appendChild(messageElement);

        if (event.data && (event.data.type === 'authorization-github' ||
            (typeof event.data === 'string' && event.data.includes('authorization:github:success')))) {
          messageElement.className = 'success';
          messageElement.innerHTML = '<strong>✓ OAuth Success!</strong> Token received.';

          if (popup) {
            popup.close();
          }
        }
      });
    }

    function testAuthDirect() {
      window.open('/auth', '_blank');
    }

    // Check if auth endpoint is accessible
    fetch('/auth')
      .then(response => {
        const statusDiv = document.getElementById('config-status');
        if (response.ok || response.status === 302) {
          statusDiv.innerHTML = '<div class="success">✓ Auth endpoint is accessible</div>';
        } else {
          statusDiv.innerHTML = '<div class="error">✗ Auth endpoint returned status: ' + response.status + '</div>';
        }
      })
      .catch(error => {
        document.getElementById('config-status').innerHTML =
          '<div class="error">✗ Auth endpoint error: ' + error.message + '</div>';
      });

    // Check config endpoint
    fetch('/admin/config.yml')
      .then(response => response.text())
      .then(config => {
        const statusDiv = document.getElementById('config-status');
        statusDiv.innerHTML += '<div class="success">✓ Config endpoint accessible</div>';
        statusDiv.innerHTML += '<div class="info"><strong>Config preview:</strong><br><pre>' +
          config.split('\\n').slice(0, 10).join('\\n') + '</pre></div>';
      })
      .catch(error => {
        document.getElementById('config-status').innerHTML +=
          '<div class="error">✗ Config endpoint error: ' + error.message + '</div>';
      });
  </script>
</body>
</html>`;

  return new Response(html, {
    status: 200,
    headers: {
      'Content-Type': 'text/html',
    },
  });
};
