---
import Layout from '../layouts/Layout.astro';
import ProjectCard from '../components/ProjectCard.astro';
import { getCollection } from 'astro:content';

const allProjects = await getCollection('projects');
const projects = allProjects.sort((a, b) => b.data.publishDate.getTime() - a.data.publishDate.getTime());

// Get unique technologies for filtering
const allTechnologies = [...new Set(projects.flatMap(project => project.data.technologies))].sort();
---

<Layout title="Projects - Guntur" description="Explore my portfolio of web development projects and applications.">
  <div class="projects-header">
    <h1 class="page-title">My Projects</h1>
    <p class="page-description">
      A collection of projects I've worked on, showcasing various technologies and approaches to problem-solving.
    </p>
  </div>

  {projects.length > 0 ? (
    <div class="projects-container">
      <!-- Filter Section -->
      <div class="filters">
        <h3>Filter by Technology:</h3>
        <div class="filter-tags">
          <button class="filter-tag active" data-filter="all">All</button>
          {allTechnologies.map((tech) => (
            <button class="filter-tag" data-filter={tech.toLowerCase()}>{tech}</button>
          ))}
        </div>
      </div>

      <!-- Projects Grid -->
      <div class="projects-grid" id="projects-grid">
        {projects.map((project) => (
          <div class="project-item" data-technologies={project.data.technologies.map(t => t.toLowerCase()).join(',')}>
            <ProjectCard
              title={project.data.title}
              description={project.data.description}
              image={project.data.image}
              technologies={project.data.technologies}
              liveUrl={project.data.liveUrl}
              githubUrl={project.data.githubUrl}
              slug={project.slug}
              featured={project.data.featured}
            />
          </div>
        ))}
      </div>
    </div>
  ) : (
    <div class="empty-state">
      <h2>No Projects Yet</h2>
      <p>Projects will appear here once they're added through the CMS.</p>
    </div>
  )}
</Layout>

<style>
  .projects-header {
    text-align: center;
    margin-bottom: 4rem;
  }

  .page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-color);
  }

  .page-description {
    font-size: 1.25rem;
    color: var(--secondary-color);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }

  .projects-container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .filters {
    margin-bottom: 3rem;
    text-align: center;
  }

  .filters h3 {
    margin-bottom: 1rem;
    color: var(--text-color);
    font-weight: 600;
  }

  .filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: center;
  }

  .filter-tag {
    background: var(--hover-color);
    border: 1px solid var(--border-color);
    color: var(--secondary-color);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
  }

  .filter-tag:hover,
  .filter-tag.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
  }

  .projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
  }

  .project-item {
    transition: opacity 0.3s ease, transform 0.3s ease;
  }

  .project-item.hidden {
    opacity: 0;
    transform: scale(0.95);
    pointer-events: none;
  }

  .empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--secondary-color);
  }

  .empty-state h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--text-color);
  }

  @media (max-width: 768px) {
    .page-title {
      font-size: 2.5rem;
    }

    .page-description {
      font-size: 1.125rem;
    }

    .projects-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .filter-tags {
      gap: 0.5rem;
    }

    .filter-tag {
      padding: 0.375rem 0.75rem;
      font-size: 0.875rem;
    }
  }
</style>

<script>
  // Client-side filtering functionality
  document.addEventListener('DOMContentLoaded', function() {
    const filterTags = document.querySelectorAll('.filter-tag');
    const projectItems = document.querySelectorAll('.project-item');

    filterTags.forEach(tag => {
      tag.addEventListener('click', function() {
        // Remove active class from all tags
        filterTags.forEach(t => t.classList.remove('active'));
        // Add active class to clicked tag
        this.classList.add('active');

        const filter = this.getAttribute('data-filter');

        projectItems.forEach(item => {
          if (filter === 'all') {
            item.classList.remove('hidden');
          } else {
            const technologies = item.getAttribute('data-technologies');
            if (technologies && technologies.includes(filter)) {
              item.classList.remove('hidden');
            } else {
              item.classList.add('hidden');
            }
          }
        });
      });
    });
  });
</script>
