---
// This page serves the Decap CMS admin interface at /admin/
// It's the same as /admin but handles the trailing slash route
---

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Content Manager</title>
  <meta name="robots" content="noindex">
</head>
<body>
  <!-- Include the script that builds the page and powers Decap CMS -->
  <script src="https://unpkg.com/decap-cms@^3.0.0/dist/decap-cms.js"></script>
  <script>
    // Enable debug logging
    console.log('Decap CMS Admin page loading...');

    // Check if we need to redirect to the proper hash URL for Decap CMS
    if (window.location.hash === '' || window.location.hash === '#') {
      // Redirect to the correct admin URL with proper hash routing
      window.location.replace('/admin/#/');
    }

    // Load and log the config
    fetch('/admin/config.yml')
      .then(response => response.text())
      .then(config => {
        console.log('Loaded config:', config);
      })
      .catch(error => {
        console.error('Failed to load config:', error);
      });

    // Initialize Decap CMS with debug logging
    console.log('Initializing Decap CMS...');
    CMS.init({
      config: {
        load_config_file: true
      }
    });

    // Monitor CMS events
    CMS.registerEventListener({
      name: 'login',
      handler: ({ author }) => {
        console.log('CMS Login event:', author);
      }
    });

    // Handle external OAuth messages
    window.addEventListener('message', function(event) {
      console.log('Received message:', event);

      if (event.origin !== window.location.origin) {
        console.log('Message from different origin, ignoring:', event.origin);
        return;
      }

      const message = event.data;
      console.log('Processing message:', message);

      if (typeof message === 'string' && message.startsWith('authorization:github:')) {
        console.log('Received OAuth message:', message);

        if (message.includes(':success:')) {
          const tokenData = JSON.parse(message.split(':success:')[1]);
          console.log('OAuth success:', tokenData);

          // The CMS should handle this automatically, but we can add custom logic here if needed
        } else if (message.includes(':error:')) {
          const errorData = JSON.parse(message.split(':error:')[1]);
          console.error('OAuth error:', errorData);

          // Show error message to user
          alert('Authentication failed: ' + errorData.error);
        }
      } else if (message && message.type && message.type.includes('authorization')) {
        console.log('Received authorization message:', message);
      }
    });

    // Debug popup opening
    const originalOpen = window.open;
    window.open = function(...args) {
      console.log('window.open called with:', args);
      console.log('URL being opened:', args[0]);
      console.log('Window name:', args[1]);
      console.log('Window features:', args[2]);

      // Check if this is the auth popup
      if (args[0] && (args[0].includes('/auth') || args[0] === 'about:blank')) {
        console.log('🚨 AUTH POPUP DETECTED!');
        console.log('Full URL:', args[0]);

        // If it's about:blank, that's the problem!
        if (args[0] === 'about:blank') {
          console.error('❌ PROBLEM: Decap CMS is trying to open about:blank instead of the auth URL');
          console.log('This suggests a configuration issue with the auth endpoint');
        }
      }

      return originalOpen.apply(this, args);
    };
  </script>
</body>
</html>
