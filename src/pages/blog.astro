---
import Layout from '../layouts/Layout.astro';
import BlogCard from '../components/BlogCard.astro';
import { getCollection } from 'astro:content';

const allBlogPosts = await getCollection('blog');
const posts = allBlogPosts
  .filter(post => !post.data.draft)
  .sort((a, b) => b.data.publishDate.getTime() - a.data.publishDate.getTime());

// Get unique tags for filtering
const allTags = [...new Set(posts.flatMap(post => post.data.tags))].sort();
---

<Layout title="Blog - Guntur" description="Read my thoughts on web development, technology, and programming.">
  <div class="blog-header">
    <h1 class="page-title">Blog</h1>
    <p class="page-description">
      Thoughts, tutorials, and insights about web development, technology, and my journey as a software developer.
    </p>
  </div>

  {posts.length > 0 ? (
    <div class="blog-container">
      <!-- Filter Section -->
      {allTags.length > 0 && (
        <div class="filters">
          <h3>Filter by Tag:</h3>
          <div class="filter-tags">
            <button class="filter-tag active" data-filter="all">All</button>
            {allTags.map((tag) => (
              <button class="filter-tag" data-filter={tag.toLowerCase()}>{tag}</button>
            ))}
          </div>
        </div>
      )}

      <!-- Blog Posts Grid -->
      <div class="posts-grid" id="posts-grid">
        {posts.map((post) => (
          <div class="post-item" data-tags={post.data.tags.map(t => t.toLowerCase()).join(',')}>
            <BlogCard
              title={post.data.title}
              description={post.data.description}
              publishDate={post.data.publishDate}
              image={post.data.image}
              tags={post.data.tags}
              slug={post.slug}
              featured={post.data.featured}
            />
          </div>
        ))}
      </div>
    </div>
  ) : (
    <div class="empty-state">
      <h2>No Blog Posts Yet</h2>
      <p>Blog posts will appear here once they're published through the CMS.</p>
    </div>
  )}
</Layout>

<style>
  .blog-header {
    text-align: center;
    margin-bottom: 4rem;
  }

  .page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-color);
  }

  .page-description {
    font-size: 1.25rem;
    color: var(--secondary-color);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }

  .blog-container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .filters {
    margin-bottom: 3rem;
    text-align: center;
  }

  .filters h3 {
    margin-bottom: 1rem;
    color: var(--text-color);
    font-weight: 600;
  }

  .filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: center;
  }

  .filter-tag {
    background: var(--hover-color);
    border: 1px solid var(--border-color);
    color: var(--secondary-color);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
  }

  .filter-tag:hover,
  .filter-tag.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
  }

  .posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
  }

  .post-item {
    transition: opacity 0.3s ease, transform 0.3s ease;
  }

  .post-item.hidden {
    opacity: 0;
    transform: scale(0.95);
    pointer-events: none;
  }

  .empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--secondary-color);
  }

  .empty-state h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--text-color);
  }

  @media (max-width: 768px) {
    .page-title {
      font-size: 2.5rem;
    }

    .page-description {
      font-size: 1.125rem;
    }

    .posts-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .filter-tags {
      gap: 0.5rem;
    }

    .filter-tag {
      padding: 0.375rem 0.75rem;
      font-size: 0.875rem;
    }
  }
</style>

<script>
  // Client-side filtering functionality
  document.addEventListener('DOMContentLoaded', function() {
    const filterTags = document.querySelectorAll('.filter-tag');
    const postItems = document.querySelectorAll('.post-item');

    filterTags.forEach(tag => {
      tag.addEventListener('click', function() {
        // Remove active class from all tags
        filterTags.forEach(t => t.classList.remove('active'));
        // Add active class to clicked tag
        this.classList.add('active');

        const filter = this.getAttribute('data-filter');

        postItems.forEach(item => {
          if (filter === 'all') {
            item.classList.remove('hidden');
          } else {
            const tags = item.getAttribute('data-tags');
            if (tags && tags.includes(filter)) {
              item.classList.remove('hidden');
            } else {
              item.classList.add('hidden');
            }
          }
        });
      });
    });
  });
</script>
