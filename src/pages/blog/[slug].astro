---
import Layout from '../../layouts/Layout.astro';
import { getCollection } from 'astro:content';

export async function getStaticPaths() {
  const posts = await getCollection('blog');
  return posts
    .filter(post => !post.data.draft)
    .map((post) => ({
      params: { slug: post.slug },
      props: { post },
    }));
}

const { post } = Astro.props;
const { Content } = await post.render();

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(date);
};
---

<Layout 
  title={`${post.data.title} - Blog`} 
  description={post.data.description}
  image={post.data.image}
>
  <article class="blog-post">
    <!-- Back Navigation -->
    <nav class="breadcrumb">
      <a href="/blog" class="back-link">← Back to Blog</a>
    </nav>

    <!-- Post Header -->
    <header class="post-header">
      <h1 class="post-title">{post.data.title}</h1>
      
      <div class="post-meta">
        <div class="meta-item">
          <span class="meta-label">Published:</span>
          <time datetime={post.data.publishDate.toISOString()}>
            {formatDate(post.data.publishDate)}
          </time>
        </div>
        {post.data.updatedDate && (
          <div class="meta-item">
            <span class="meta-label">Updated:</span>
            <time datetime={post.data.updatedDate.toISOString()}>
              {formatDate(post.data.updatedDate)}
            </time>
          </div>
        )}
        <div class="meta-item">
          <span class="meta-label">Author:</span>
          <span>{post.data.author}</span>
        </div>
      </div>

      <p class="post-description">{post.data.description}</p>

      <!-- Tags -->
      {post.data.tags.length > 0 && (
        <div class="post-tags">
          <span class="tags-label">Tags:</span>
          <div class="tags-list">
            {post.data.tags.map((tag) => (
              <span class="tag">{tag}</span>
            ))}
          </div>
        </div>
      )}
    </header>

    <!-- Featured Image -->
    {post.data.image && (
      <div class="post-image">
        <img src={post.data.image} alt={post.data.title} />
      </div>
    )}

    <!-- Post Content -->
    <div class="post-content">
      <Content />
    </div>

    <!-- Post Footer -->
    <footer class="post-footer">
      <div class="share-section">
        <h3>Share this post</h3>
        <div class="share-buttons">
          <a 
            href={`https://twitter.com/intent/tweet?text=${encodeURIComponent(post.data.title)}&url=${encodeURIComponent(Astro.url.href)}`}
            target="_blank" 
            rel="noopener noreferrer"
            class="share-button twitter"
          >
            Twitter
          </a>
          <a 
            href={`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(Astro.url.href)}`}
            target="_blank" 
            rel="noopener noreferrer"
            class="share-button linkedin"
          >
            LinkedIn
          </a>
        </div>
      </div>
    </footer>
  </article>
</Layout>

<style>
  .blog-post {
    max-width: 800px;
    margin: 0 auto;
  }

  .breadcrumb {
    margin-bottom: 2rem;
  }

  .back-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
  }

  .back-link:hover {
    color: #1d4ed8;
  }

  .post-header {
    margin-bottom: 3rem;
  }

  .post-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--text-color);
    line-height: 1.2;
  }

  .post-meta {
    display: flex;
    gap: 2rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    color: var(--secondary-color);
    font-size: 0.875rem;
  }

  .meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .meta-label {
    font-weight: 600;
    color: var(--text-color);
  }

  .post-description {
    font-size: 1.25rem;
    color: var(--secondary-color);
    margin-bottom: 2rem;
    line-height: 1.6;
    font-style: italic;
  }

  .post-tags {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .tags-label {
    font-weight: 600;
    color: var(--text-color);
  }

  .tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .tag {
    background: var(--hover-color);
    color: var(--secondary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .post-image {
    margin-bottom: 3rem;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
  }

  .post-image img {
    width: 100%;
    height: auto;
    display: block;
  }

  .post-content {
    line-height: 1.7;
    color: var(--text-color);
    margin-bottom: 3rem;
  }

  .post-content :global(h2) {
    margin-top: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
    font-size: 1.875rem;
  }

  .post-content :global(h3) {
    margin-top: 2rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
    font-size: 1.5rem;
  }

  .post-content :global(h4) {
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
    font-size: 1.25rem;
  }

  .post-content :global(p) {
    margin-bottom: 1.5rem;
  }

  .post-content :global(ul),
  .post-content :global(ol) {
    margin-bottom: 1.5rem;
    padding-left: 1.5rem;
  }

  .post-content :global(li) {
    margin-bottom: 0.5rem;
  }

  .post-content :global(blockquote) {
    border-left: 4px solid var(--primary-color);
    padding-left: 1rem;
    margin: 1.5rem 0;
    font-style: italic;
    color: var(--secondary-color);
  }

  .post-content :global(code) {
    background: var(--hover-color);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 0.875rem;
  }

  .post-content :global(pre) {
    background: var(--hover-color);
    padding: 1.5rem;
    border-radius: 8px;
    overflow-x: auto;
    margin-bottom: 1.5rem;
  }

  .post-content :global(pre code) {
    background: none;
    padding: 0;
  }

  .post-content :global(img) {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 1.5rem 0;
  }

  .post-footer {
    border-top: 1px solid var(--border-color);
    padding-top: 2rem;
  }

  .share-section h3 {
    margin-bottom: 1rem;
    color: var(--text-color);
    font-weight: 600;
  }

  .share-buttons {
    display: flex;
    gap: 1rem;
  }

  .share-button {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 0.875rem;
  }

  .share-button.twitter {
    background: #1da1f2;
    color: white;
  }

  .share-button.twitter:hover {
    background: #1a91da;
  }

  .share-button.linkedin {
    background: #0077b5;
    color: white;
  }

  .share-button.linkedin:hover {
    background: #006ba1;
  }

  @media (max-width: 768px) {
    .post-title {
      font-size: 2.5rem;
    }

    .post-description {
      font-size: 1.125rem;
    }

    .post-meta {
      flex-direction: column;
      gap: 0.5rem;
    }

    .post-tags {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.75rem;
    }

    .share-buttons {
      flex-direction: column;
    }

    .share-button {
      text-align: center;
    }
  }
</style>
