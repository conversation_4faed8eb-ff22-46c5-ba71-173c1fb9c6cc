---
import Layout from '../layouts/Layout.astro';
import ProjectCard from '../components/ProjectCard.astro';
import BlogCard from '../components/BlogCard.astro';
import { getCollection } from 'astro:content';

// Get featured projects and recent blog posts
const allProjects = await getCollection('projects');
const featuredProjects = allProjects
  .filter(project => project.data.featured)
  .sort((a, b) => b.data.publishDate.getTime() - a.data.publishDate.getTime())
  .slice(0, 3);

const allBlogPosts = await getCollection('blog');
const recentPosts = allBlogPosts
  .filter(post => !post.data.draft)
  .sort((a, b) => b.data.publishDate.getTime() - a.data.publishDate.getTime())
  .slice(0, 3);
---

<Layout title="Guntur - Software Developer" description="Portfolio website of Guntur - Software Developer specializing in web development and modern technologies.">
  <!-- Hero Section -->
  <section class="hero">
    <div class="hero-content">
      <h1 class="hero-title">
        Hi, I'm <span class="highlight">Guntur</span>
      </h1>
      <p class="hero-subtitle">
        Software Developer passionate about creating innovative web solutions
      </p>
      <p class="hero-description">
        I specialize in modern web technologies and love building applications that make a difference.
        Welcome to my portfolio where you can explore my projects and read about my journey in tech.
      </p>
      <div class="hero-actions">
        <a href="/projects" class="btn btn-primary">View Projects</a>
        <a href="/blog" class="btn btn-secondary">Read Blog</a>
      </div>
    </div>
  </section>

  <!-- Featured Projects Section -->
  {featuredProjects.length > 0 && (
    <section class="featured-projects">
      <h2 class="section-title">Featured Projects</h2>
      <div class="projects-grid">
        {featuredProjects.map((project) => (
          <ProjectCard
            title={project.data.title}
            description={project.data.description}
            image={project.data.image}
            technologies={project.data.technologies}
            liveUrl={project.data.liveUrl}
            githubUrl={project.data.githubUrl}
            slug={project.slug}
            featured={project.data.featured}
          />
        ))}
      </div>
      <div class="section-footer">
        <a href="/projects" class="view-all-link">View All Projects →</a>
      </div>
    </section>
  )}

  <!-- Recent Blog Posts Section -->
  {recentPosts.length > 0 && (
    <section class="recent-posts">
      <h2 class="section-title">Recent Blog Posts</h2>
      <div class="posts-grid">
        {recentPosts.map((post) => (
          <BlogCard
            title={post.data.title}
            description={post.data.description}
            publishDate={post.data.publishDate}
            image={post.data.image}
            tags={post.data.tags}
            slug={post.slug}
            featured={post.data.featured}
          />
        ))}
      </div>
      <div class="section-footer">
        <a href="/blog" class="view-all-link">View All Posts →</a>
      </div>
    </section>
  )}
</Layout>

<style>
  .hero {
    text-align: center;
    padding: 4rem 0 6rem 0;
    max-width: 800px;
    margin: 0 auto;
  }

  .hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
  }

  .highlight {
    color: var(--primary-color);
  }

  .hero-subtitle {
    font-size: 1.5rem;
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
    font-weight: 500;
  }

  .hero-description {
    font-size: 1.125rem;
    color: var(--secondary-color);
    margin-bottom: 2.5rem;
    line-height: 1.7;
  }

  .hero-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
  }

  .btn {
    padding: 0.875rem 2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
  }

  .btn-primary {
    background: var(--primary-color);
    color: white;
  }

  .btn-primary:hover {
    background: #1d4ed8;
    transform: translateY(-2px);
  }

  .btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
  }

  .btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
  }

  .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 3rem;
    color: var(--text-color);
  }

  .featured-projects,
  .recent-posts {
    margin-bottom: 6rem;
  }

  .projects-grid,
  .posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
  }

  .section-footer {
    text-align: center;
  }

  .view-all-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    font-size: 1.125rem;
    transition: color 0.3s ease;
  }

  .view-all-link:hover {
    color: #1d4ed8;
  }

  @media (max-width: 768px) {
    .hero {
      padding: 2rem 0 4rem 0;
    }

    .hero-title {
      font-size: 2.5rem;
    }

    .hero-subtitle {
      font-size: 1.25rem;
    }

    .hero-description {
      font-size: 1rem;
    }

    .hero-actions {
      flex-direction: column;
      align-items: center;
    }

    .btn {
      width: 100%;
      max-width: 300px;
    }

    .section-title {
      font-size: 2rem;
    }

    .projects-grid,
    .posts-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
  }
</style>
