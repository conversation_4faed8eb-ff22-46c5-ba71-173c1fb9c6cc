{"name": "@astrojs/prism", "version": "3.3.0", "description": "Add Prism syntax highlighting support to your Astro site", "author": "<PERSON><PERSON><PERSON>", "type": "module", "license": "MIT", "bugs": "https://github.com/withastro/astro/issues", "repository": {"type": "git", "url": "git+https://github.com/withastro/astro.git", "directory": "packages/astro-prism"}, "homepage": "https://docs.astro.build/en/reference/api-reference/#prism-", "main": "dist/index.js", "exports": {".": "./dist/index.js", "./Prism.astro": "./Prism.astro", "./dist/highlighter": "./dist/highlighter.js"}, "files": ["dist", "Prism.astro"], "keywords": ["astro", "astro-component"], "dependencies": {"prismjs": "^1.30.0"}, "devDependencies": {"@types/prismjs": "1.26.5", "astro-scripts": "0.0.14"}, "engines": {"node": "18.20.8 || ^20.3.0 || >=22.0.0"}, "scripts": {"build": "astro-scripts build \"src/**/*.ts\" && tsc -p ./tsconfig.json", "build:ci": "astro-scripts build \"src/**/*.ts\"", "dev": "astro-scripts dev \"src/**/*.ts\""}}