{"name": "@astrojs/markdown-remark", "version": "6.3.2", "type": "module", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/withastro/astro.git", "directory": "packages/markdown/remark"}, "bugs": "https://github.com/withastro/astro/issues", "homepage": "https://astro.build", "main": "./dist/index.js", "exports": {".": "./dist/index.js"}, "imports": {"#import-plugin": {"browser": "./dist/import-plugin-browser.js", "default": "./dist/import-plugin-default.js"}}, "files": ["dist"], "dependencies": {"github-slugger": "^2.0.0", "hast-util-from-html": "^2.0.3", "hast-util-to-text": "^4.0.2", "import-meta-resolve": "^4.1.0", "js-yaml": "^4.1.0", "mdast-util-definitions": "^6.0.0", "rehype-raw": "^7.0.0", "rehype-stringify": "^10.0.1", "remark-gfm": "^4.0.1", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.2", "remark-smartypants": "^3.0.2", "shiki": "^3.2.1", "smol-toml": "^1.3.1", "unified": "^11.0.5", "unist-util-remove-position": "^5.0.0", "unist-util-visit": "^5.0.0", "unist-util-visit-parents": "^6.0.1", "vfile": "^6.0.3", "@astrojs/internal-helpers": "0.6.1", "@astrojs/prism": "3.3.0"}, "devDependencies": {"@types/estree": "^1.0.7", "@types/hast": "^3.0.4", "@types/js-yaml": "^4.0.9", "@types/mdast": "^4.0.4", "@types/unist": "^3.0.3", "esbuild": "^0.24.2", "mdast-util-mdx-expression": "^2.0.1", "astro-scripts": "0.0.14"}, "publishConfig": {"provenance": true}, "scripts": {"prepublish": "pnpm build", "build": "astro-scripts build \"src/**/*.ts\" && tsc -p tsconfig.json", "build:ci": "astro-scripts build \"src/**/*.ts\"", "dev": "astro-scripts dev \"src/**/*.ts\"", "test": "astro-scripts test \"test/**/*.test.js\""}}