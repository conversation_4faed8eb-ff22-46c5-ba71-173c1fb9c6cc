{"name": "@astrojs/telemetry", "version": "3.3.0", "type": "module", "types": "./dist/index.d.ts", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/withastro/astro.git", "directory": "packages/telemetry"}, "bugs": "https://github.com/withastro/astro/issues", "homepage": "https://astro.build", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "files": ["dist"], "dependencies": {"ci-info": "^4.2.0", "debug": "^4.4.0", "dlv": "^1.1.3", "dset": "^3.1.4", "is-docker": "^3.0.0", "is-wsl": "^3.1.0", "which-pm-runs": "^1.1.0"}, "devDependencies": {"@types/debug": "^4.1.12", "@types/dlv": "^1.1.5", "@types/node": "^18.17.8", "@types/which-pm-runs": "^1.0.2", "astro-scripts": "0.0.14"}, "engines": {"node": "18.20.8 || ^20.3.0 || >=22.0.0"}, "publishConfig": {"provenance": true}, "scripts": {"build": "astro-scripts build \"src/**/*.ts\" && tsc", "build:ci": "astro-scripts build \"src/**/*.ts\"", "dev": "astro-scripts dev \"src/**/*.ts\"", "test": "astro-scripts test \"test/**/*.test.js\""}}