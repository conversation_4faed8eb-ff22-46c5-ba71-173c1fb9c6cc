{"name": "yocto-spinner", "version": "0.2.3", "description": "Tiny terminal spinner", "license": "MIT", "repository": "sindresorhus/yocto-spinner", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18.19"}, "scripts": {"test": "xo && ava && tsc index.d.ts"}, "files": ["index.js", "index.d.ts"], "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle", "tiny", "yocto", "micro", "nano"], "dependencies": {"yoctocolors": "^2.1.1"}, "devDependencies": {"ava": "^6.1.3", "typescript": "^5.5.4", "xo": "^0.59.3"}, "xo": {"rules": {"unicorn/no-process-exit": "off"}}}