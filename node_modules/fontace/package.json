{"name": "fontace", "version": "0.3.0", "description": "Extract useful information from font files", "type": "module", "sideEffects": false, "exports": {".": "./dist/index.js"}, "files": ["dist"], "keywords": ["font", "fontkit", "opentype", "truetype", "woff", "woff2", "css", "font-face"], "author": "delucis", "repository": {"type": "git", "url": "git+https://github.com/delucis/fontace.git"}, "bugs": "https://github.com/delucis/fontace/issues", "homepage": "https://github.com/delucis/fontace#readme", "license": "MIT", "dependencies": {"@types/fontkit": "^2.0.8", "fontkit": "^2.0.4"}, "devDependencies": {"@changesets/changelog-github": "^0.5.1", "@changesets/cli": "^2.28.1", "@fontsource-variable/inter": "^5.2.5", "@fontsource/roboto": "^5.2.5", "tsup": "^8.4.0", "typescript": "^5.8.2", "vite": "^6.2.3", "vitest": "^3.0.9"}, "scripts": {"build": "tsup src/index.ts --format esm --dts --clean", "test": "vitest", "ci-version": "changeset version && pnpm install --no-frozen-lockfile", "ci-publish": "changeset publish"}}