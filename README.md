# Portfolio Website with Astro & Decap CMS

A modern, fast, and SEO-optimized portfolio website built with Astro framework and integrated with Decap CMS for easy content management.

## ✨ Features

- **⚡ Lightning Fast**: Built with Astro for optimal performance
- **📝 Content Management**: Integrated with Decap CMS for easy editing
- **📱 Responsive Design**: Mobile-first approach with modern CSS
- **🎨 Clean Design**: Professional and minimalist design system
- **🔍 SEO Optimized**: Proper meta tags and structured data
- **🚀 Vercel Ready**: Optimized for Vercel deployment

## 🏗️ Project Structure

```text
/
├── public/
│   ├── admin/              # Decap CMS admin interface
│   ├── images/             # Static images
│   └── favicon.svg
├── src/
│   ├── components/         # Reusable components
│   │   ├── BlogCard.astro
│   │   └── ProjectCard.astro
│   ├── content/            # Content collections
│   │   ├── blog/           # Blog posts
│   │   ├── projects/       # Project showcases
│   │   └── config.ts       # Content schema definitions
│   ├── layouts/            # Page layouts
│   │   └── Layout.astro
│   └── pages/              # File-based routing
│       ├── blog/
│       ├── projects/
│       └── index.astro
├── astro.config.mjs        # Astro configuration
├── vercel.json            # Vercel deployment config
└── package.json
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone https://github.com/bagusfarisa/bagusfarisa-astro.git
cd bagusfarisa-astro
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open [http://localhost:4321](http://localhost:4321) in your browser

## 📝 Content Management

### Using Decap CMS

1. **Access the CMS**: Navigate to `/admin` or `/admin/` on your deployed site
2. **Authentication**: Set up Netlify Identity or GitHub authentication
3. **Create Content**: Use the intuitive interface to add projects and blog posts
4. **Preview**: Preview your content before publishing

### Manual Content Creation

You can also create content manually by adding Markdown files to:
- `src/content/projects/` for project showcases
- `src/content/blog/` for blog posts

## 🛠️ Commands

| Command                   | Action                                           |
| :------------------------ | :----------------------------------------------- |
| `npm install`             | Installs dependencies                            |
| `npm run dev`             | Starts local dev server at `localhost:4321`     |
| `npm run build`           | Build your production site to `./dist/`          |
| `npm run preview`         | Preview your build locally, before deploying     |
| `npm run astro ...`       | Run CLI commands like `astro add`, `astro check` |

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Configure environment variables if needed
4. Deploy automatically on every push

### Other Platforms

The site can be deployed to any static hosting platform:
- Netlify
- GitHub Pages
- Cloudflare Pages
- AWS S3 + CloudFront

## ⚙️ Configuration

### Astro Configuration
Edit `astro.config.mjs` to customize:
- Site URL
- Build settings
- Integrations

### CMS Configuration
Edit `public/admin/config.yml` to customize:
- Content collections
- Field definitions
- Authentication settings

## 🎨 Customization

### Styling
- CSS custom properties in `src/layouts/Layout.astro`
- Component-specific styles in individual `.astro` files
- Responsive design with mobile-first approach

### Content Schema
- Modify `src/content/config.ts` to change content structure
- Update CMS config to match schema changes

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions, issues, and feature requests are welcome! Feel free to check the [issues page](https://github.com/bagusfarisa/bagusfarisa-astro/issues).

## 📞 Contact

- GitHub: [@bagusfarisa](https://github.com/bagusfarisa)
- LinkedIn: [bagusfarisa](https://linkedin.com/in/bagusfarisa)

---

Built with ❤️ using [Astro](https://astro.build) and [Decap CMS](https://decapcms.org)
