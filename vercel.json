{"buildCommand": "npm run build", "outputDirectory": "dist", "devCommand": "npm run dev", "installCommand": "npm install", "framework": "astro", "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}, {"source": "/admin/(.*)", "headers": [{"key": "X-Robots-Tag", "value": "noindex"}]}], "redirects": [{"source": "/admin/index.html", "destination": "/admin/", "permanent": true}]}