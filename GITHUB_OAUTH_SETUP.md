# GitHub OAuth Setup for Decap CMS

This guide will help you set up GitHub OAuth authentication for your Decap CMS.

## Step 1: Create GitHub OAuth App

1. Go to [GitHub Developer Settings](https://github.com/settings/developers)
2. Click "New OAuth App" or edit your existing app
3. Fill in the application details:
   - **Application name**: `bagusfarisa-astro-cms` (or any name you prefer)
   - **Homepage URL**: `https://bagusfarisa.vercel.app`
   - **Application description**: `OAuth app for Decap CMS authentication`
   - **Authorization callback URL**: `https://bagusfarisa.vercel.app/auth`

4. Click "Register application" or "Update application"
5. Note down the **Client ID** (should be: `Ov23liYrLSc03pEujzBl`)
6. Generate a **Client Secret** and copy it

**Important**: Make sure the callback URL exactly matches your domain. For local testing, you may need to add `http://localhost:4321/auth` as an additional callback URL.

## Step 2: Configure Environment Variables

### For Local Development:
1. Create a `.env` file in your project root:
```bash
GITHUB_CLIENT_SECRET=your_actual_client_secret_here
```

### For Vercel Deployment:
1. Go to your Vercel project dashboard
2. Navigate to Settings → Environment Variables
3. Add a new environment variable:
   - **Name**: `GITHUB_CLIENT_SECRET`
   - **Value**: Your actual GitHub client secret
   - **Environment**: Production (and Preview if needed)

## Step 3: Verify Configuration

Your `public/admin/config.yml` should have:
```yaml
backend:
  name: github
  repo: bagusfarisa/bagusfarisa-astro
  branch: main
  auth_type: external_oauth
  app_id: Ov23liYrLSc03pEujzBl
  base_url: https://bagusfarisa.vercel.app
  auth_endpoint: auth
```

## Step 4: Test the Setup

1. Deploy your changes to Vercel
2. Go to `https://bagusfarisa.vercel.app/admin/`
3. Click "Login with GitHub"
4. You should be redirected to GitHub for authorization
5. After authorizing, you should be redirected back to the CMS

## Troubleshooting

### Common Issues:

1. **404 Error on /auth**: Make sure the auth endpoint is deployed
2. **Blank popup window**: This was the original issue - now fixed with proper HTML response
3. **Client Secret Error**: Verify the environment variable is set correctly
4. **Callback URL Mismatch**: Ensure the GitHub OAuth app callback URL matches your domain
5. **Popup blocked**: Some browsers may block popups - check browser settings
6. **CORS Issues**: The auth endpoint handles CORS automatically

### Debug Steps:

1. Check Vercel function logs for any errors
2. Verify the GitHub OAuth app settings
3. Ensure the client secret environment variable is set
4. Test the auth endpoint directly: `https://bagusfarisa.vercel.app/auth`

## Security Notes

- Never commit the client secret to your repository
- Use environment variables for all sensitive data
- The client secret should only be accessible server-side
- Regularly rotate your OAuth credentials if needed
