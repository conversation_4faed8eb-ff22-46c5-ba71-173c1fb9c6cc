<!DOCTYPE html><html> <head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Content Manager</title><meta name="robots" content="noindex"></head> <body> <!-- Include the script that builds the page and powers Decap CMS --> <script type="module">console.log("Decap CMS Admin page loading...");(window.location.hash===""||window.location.hash==="#")&&window.location.replace("/admin/#/");fetch("/admin/config.yml").then(o=>o.text()).then(o=>{console.log("Loaded config:",o)}).catch(o=>{console.error("Failed to load config:",o)});console.log("Initializing Decap CMS...");CMS.init({config:{load_config_file:!0}});CMS.registerEventListener({name:"login",handler:({author:o})=>{console.log("CMS Login event:",o)}});window.addEventListener("message",function(o){if(console.log("Received message:",o),o.origin!==window.location.origin){console.log("Message from different origin, ignoring:",o.origin);return}const e=o.data;if(console.log("Processing message:",e),typeof e=="string"&&e.startsWith("authorization:github:")){if(console.log("Received OAuth message:",e),e.includes(":success:")){const n=JSON.parse(e.split(":success:")[1]);console.log("OAuth success:",n)}else if(e.includes(":error:")){const n=JSON.parse(e.split(":error:")[1]);console.error("OAuth error:",n),alert("Authentication failed: "+n.error)}}else e&&e.type&&e.type.includes("authorization")&&console.log("Received authorization message:",e)});const i=window.open;window.open=function(...o){return console.log("window.open called with:",o),console.log("URL being opened:",o[0]),console.log("Window name:",o[1]),console.log("Window features:",o[2]),o[0]&&(o[0].includes("/auth")||o[0]==="about:blank")&&(console.log("🚨 AUTH POPUP DETECTED!"),console.log("Full URL:",o[0]),o[0]==="about:blank"&&(console.error("❌ PROBLEM: Decap CMS is trying to open about:blank instead of the auth URL"),console.log("This suggests a configuration issue with the auth endpoint"))),i.apply(this,o)};</script> <script type="module" src="/_astro/index.astro_astro_type_script_index_1_lang.ClYIQhV0.js"></script> </body> </html>