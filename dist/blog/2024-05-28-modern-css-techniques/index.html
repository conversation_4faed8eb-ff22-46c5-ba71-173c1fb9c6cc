<!DOCTYPE html><html lang="en" data-astro-cid-sckkx6r4> <head><meta charset="UTF-8"><meta name="description" content="Explore the latest CSS features and techniques that can improve your web development workflow and create better user experiences."><meta name="viewport" content="width=device-width, initial-scale=1.0"><link rel="icon" type="image/svg+xml" href="/favicon.svg"><meta name="generator" content="Astro v5.8.2"><!-- Open Graph / Facebook --><meta property="og:type" content="website"><meta property="og:url" content="https://bagusfarisa-astro.vercel.app/blog/2024-05-28-modern-css-techniques/"><meta property="og:title" content="Modern CSS Techniques Every Developer Should Know - Blog"><meta property="og:description" content="Explore the latest CSS features and techniques that can improve your web development workflow and create better user experiences."><meta property="og:image" content="https://bagusfarisa-astro.vercel.app/images/css-techniques-cover.jpg"><!-- Twitter --><meta property="twitter:card" content="summary_large_image"><meta property="twitter:url" content="https://bagusfarisa-astro.vercel.app/blog/2024-05-28-modern-css-techniques/"><meta property="twitter:title" content="Modern CSS Techniques Every Developer Should Know - Blog"><meta property="twitter:description" content="Explore the latest CSS features and techniques that can improve your web development workflow and create better user experiences."><meta property="twitter:image" content="https://bagusfarisa-astro.vercel.app/images/css-techniques-cover.jpg"><title>Modern CSS Techniques Every Developer Should Know - Blog</title><style>:root{--primary-color: #2563eb;--secondary-color: #64748b;--text-color: #1e293b;--bg-color: #ffffff;--border-color: #e2e8f0;--hover-color: #f8fafc}[data-astro-cid-sckkx6r4]{margin:0;padding:0;box-sizing:border-box}body{font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,sans-serif;line-height:1.6;color:var(--text-color);background-color:var(--bg-color)}.navbar[data-astro-cid-sckkx6r4]{background:var(--bg-color);border-bottom:1px solid var(--border-color);position:sticky;top:0;z-index:100}.nav-container[data-astro-cid-sckkx6r4]{max-width:1200px;margin:0 auto;padding:1rem 2rem;display:flex;justify-content:space-between;align-items:center}.nav-logo[data-astro-cid-sckkx6r4]{font-size:1.5rem;font-weight:700;color:var(--primary-color);text-decoration:none}.nav-menu[data-astro-cid-sckkx6r4]{display:flex;list-style:none;gap:2rem}.nav-link[data-astro-cid-sckkx6r4]{color:var(--text-color);text-decoration:none;font-weight:500;transition:color .3s ease}.nav-link[data-astro-cid-sckkx6r4]:hover{color:var(--primary-color)}main[data-astro-cid-sckkx6r4]{min-height:calc(100vh - 140px);max-width:1200px;margin:0 auto;padding:2rem}.footer[data-astro-cid-sckkx6r4]{background:var(--hover-color);border-top:1px solid var(--border-color);margin-top:4rem}.footer-container[data-astro-cid-sckkx6r4]{max-width:1200px;margin:0 auto;padding:2rem;display:flex;justify-content:space-between;align-items:center;flex-wrap:wrap;gap:1rem}.social-links[data-astro-cid-sckkx6r4]{display:flex;gap:1rem}.social-links[data-astro-cid-sckkx6r4] a[data-astro-cid-sckkx6r4]{color:var(--secondary-color);text-decoration:none;transition:color .3s ease}.social-links[data-astro-cid-sckkx6r4] a[data-astro-cid-sckkx6r4]:hover{color:var(--primary-color)}@media (max-width: 768px){.nav-container[data-astro-cid-sckkx6r4]{padding:1rem}.nav-menu[data-astro-cid-sckkx6r4]{gap:1rem}main[data-astro-cid-sckkx6r4]{padding:1rem}.footer-container[data-astro-cid-sckkx6r4]{flex-direction:column;text-align:center}}
.blog-post[data-astro-cid-4sn4zg3r]{max-width:800px;margin:0 auto}.breadcrumb[data-astro-cid-4sn4zg3r]{margin-bottom:2rem}.back-link[data-astro-cid-4sn4zg3r]{color:var(--primary-color);text-decoration:none;font-weight:500;transition:color .3s ease}.back-link[data-astro-cid-4sn4zg3r]:hover{color:#1d4ed8}.post-header[data-astro-cid-4sn4zg3r]{margin-bottom:3rem}.post-title[data-astro-cid-4sn4zg3r]{font-size:3rem;font-weight:700;margin-bottom:1.5rem;color:var(--text-color);line-height:1.2}.post-meta[data-astro-cid-4sn4zg3r]{display:flex;gap:2rem;margin-bottom:1.5rem;flex-wrap:wrap;color:var(--secondary-color);font-size:.875rem}.meta-item[data-astro-cid-4sn4zg3r]{display:flex;align-items:center;gap:.5rem}.meta-label[data-astro-cid-4sn4zg3r]{font-weight:600;color:var(--text-color)}.post-description[data-astro-cid-4sn4zg3r]{font-size:1.25rem;color:var(--secondary-color);margin-bottom:2rem;line-height:1.6;font-style:italic}.post-tags[data-astro-cid-4sn4zg3r]{display:flex;align-items:center;gap:1rem;flex-wrap:wrap}.tags-label[data-astro-cid-4sn4zg3r]{font-weight:600;color:var(--text-color)}.tags-list[data-astro-cid-4sn4zg3r]{display:flex;flex-wrap:wrap;gap:.5rem}.tag[data-astro-cid-4sn4zg3r]{background:var(--hover-color);color:var(--secondary-color);padding:.25rem .75rem;border-radius:20px;font-size:.875rem;font-weight:500}.post-image[data-astro-cid-4sn4zg3r]{margin-bottom:3rem;border-radius:12px;overflow:hidden;box-shadow:0 10px 25px -3px #0000001a}.post-image[data-astro-cid-4sn4zg3r] img[data-astro-cid-4sn4zg3r]{width:100%;height:auto;display:block}.post-content[data-astro-cid-4sn4zg3r]{line-height:1.7;color:var(--text-color);margin-bottom:3rem}.post-content[data-astro-cid-4sn4zg3r] h2{margin-top:2.5rem;margin-bottom:1rem;font-weight:600;font-size:1.875rem}.post-content[data-astro-cid-4sn4zg3r] h3{margin-top:2rem;margin-bottom:.75rem;font-weight:600;font-size:1.5rem}.post-content[data-astro-cid-4sn4zg3r] h4{margin-top:1.5rem;margin-bottom:.5rem;font-weight:600;font-size:1.25rem}.post-content[data-astro-cid-4sn4zg3r] p{margin-bottom:1.5rem}.post-content[data-astro-cid-4sn4zg3r] ul,.post-content[data-astro-cid-4sn4zg3r] ol{margin-bottom:1.5rem;padding-left:1.5rem}.post-content[data-astro-cid-4sn4zg3r] li{margin-bottom:.5rem}.post-content[data-astro-cid-4sn4zg3r] blockquote{border-left:4px solid var(--primary-color);padding-left:1rem;margin:1.5rem 0;font-style:italic;color:var(--secondary-color)}.post-content[data-astro-cid-4sn4zg3r] code{background:var(--hover-color);padding:.25rem .5rem;border-radius:4px;font-family:Monaco,Menlo,monospace;font-size:.875rem}.post-content[data-astro-cid-4sn4zg3r] pre{background:var(--hover-color);padding:1.5rem;border-radius:8px;overflow-x:auto;margin-bottom:1.5rem}.post-content[data-astro-cid-4sn4zg3r] pre code{background:none;padding:0}.post-content[data-astro-cid-4sn4zg3r] img{max-width:100%;height:auto;border-radius:8px;margin:1.5rem 0}.post-footer[data-astro-cid-4sn4zg3r]{border-top:1px solid var(--border-color);padding-top:2rem}.share-section[data-astro-cid-4sn4zg3r] h3[data-astro-cid-4sn4zg3r]{margin-bottom:1rem;color:var(--text-color);font-weight:600}.share-buttons[data-astro-cid-4sn4zg3r]{display:flex;gap:1rem}.share-button[data-astro-cid-4sn4zg3r]{padding:.5rem 1rem;border-radius:6px;text-decoration:none;font-weight:500;transition:all .3s ease;font-size:.875rem}.share-button[data-astro-cid-4sn4zg3r].twitter{background:#1da1f2;color:#fff}.share-button[data-astro-cid-4sn4zg3r].twitter:hover{background:#1a91da}.share-button[data-astro-cid-4sn4zg3r].linkedin{background:#0077b5;color:#fff}.share-button[data-astro-cid-4sn4zg3r].linkedin:hover{background:#006ba1}@media (max-width: 768px){.post-title[data-astro-cid-4sn4zg3r]{font-size:2.5rem}.post-description[data-astro-cid-4sn4zg3r]{font-size:1.125rem}.post-meta[data-astro-cid-4sn4zg3r]{flex-direction:column;gap:.5rem}.post-tags[data-astro-cid-4sn4zg3r]{flex-direction:column;align-items:flex-start;gap:.75rem}.share-buttons[data-astro-cid-4sn4zg3r]{flex-direction:column}.share-button[data-astro-cid-4sn4zg3r]{text-align:center}}
</style></head> <body data-astro-cid-sckkx6r4> <nav class="navbar" data-astro-cid-sckkx6r4> <div class="nav-container" data-astro-cid-sckkx6r4> <a href="/" class="nav-logo" data-astro-cid-sckkx6r4>Guntur</a> <ul class="nav-menu" data-astro-cid-sckkx6r4> <li class="nav-item" data-astro-cid-sckkx6r4> <a href="/" class="nav-link" data-astro-cid-sckkx6r4>Home</a> </li> <li class="nav-item" data-astro-cid-sckkx6r4> <a href="/projects" class="nav-link" data-astro-cid-sckkx6r4>Projects</a> </li> <li class="nav-item" data-astro-cid-sckkx6r4> <a href="/blog" class="nav-link" data-astro-cid-sckkx6r4>Blog</a> </li> </ul> </div> </nav> <main data-astro-cid-sckkx6r4>  <article class="blog-post" data-astro-cid-4sn4zg3r> <!-- Back Navigation --> <nav class="breadcrumb" data-astro-cid-4sn4zg3r> <a href="/blog" class="back-link" data-astro-cid-4sn4zg3r>← Back to Blog</a> </nav> <!-- Post Header --> <header class="post-header" data-astro-cid-4sn4zg3r> <h1 class="post-title" data-astro-cid-4sn4zg3r>Modern CSS Techniques Every Developer Should Know</h1> <div class="post-meta" data-astro-cid-4sn4zg3r> <div class="meta-item" data-astro-cid-4sn4zg3r> <span class="meta-label" data-astro-cid-4sn4zg3r>Published:</span> <time datetime="2024-05-28T14:30:00.000Z" data-astro-cid-4sn4zg3r> May 28, 2024 </time> </div>  <div class="meta-item" data-astro-cid-4sn4zg3r> <span class="meta-label" data-astro-cid-4sn4zg3r>Author:</span> <span data-astro-cid-4sn4zg3r>Guntur</span> </div> </div> <p class="post-description" data-astro-cid-4sn4zg3r>Explore the latest CSS features and techniques that can improve your web development workflow and create better user experiences.</p> <!-- Tags --> <div class="post-tags" data-astro-cid-4sn4zg3r> <span class="tags-label" data-astro-cid-4sn4zg3r>Tags:</span> <div class="tags-list" data-astro-cid-4sn4zg3r> <span class="tag" data-astro-cid-4sn4zg3r>CSS</span><span class="tag" data-astro-cid-4sn4zg3r>Web Development</span><span class="tag" data-astro-cid-4sn4zg3r>Frontend</span><span class="tag" data-astro-cid-4sn4zg3r>Design</span> </div> </div> </header> <!-- Featured Image --> <div class="post-image" data-astro-cid-4sn4zg3r> <img src="/images/css-techniques-cover.jpg" alt="Modern CSS Techniques Every Developer Should Know" data-astro-cid-4sn4zg3r> </div> <!-- Post Content --> <div class="post-content" data-astro-cid-4sn4zg3r> <h1 id="modern-css-techniques-every-developer-should-know">Modern CSS Techniques Every Developer Should Know</h1>
<p>CSS has evolved tremendously over the past few years, introducing powerful new features that make styling more intuitive and maintainable. Let’s explore some modern CSS techniques that every developer should have in their toolkit.</p>
<h2 id="css-custom-properties-variables">CSS Custom Properties (Variables)</h2>
<p>CSS custom properties have revolutionized how we handle theming and maintainable styles:</p>
<pre class="astro-code github-dark" style="background-color:#24292e;color:#e1e4e8; overflow-x: auto;" tabindex="0" data-language="css"><code><span class="line"><span style="color:#B392F0">:root</span><span style="color:#E1E4E8"> {</span></span>
<span class="line"><span style="color:#FFAB70">  --primary-color</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">#2563eb</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#FFAB70">  --secondary-color</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">#64748b</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#FFAB70">  --border-radius</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">8</span><span style="color:#F97583">px</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#FFAB70">  --spacing-unit</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">1</span><span style="color:#F97583">rem</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#B392F0">.button</span><span style="color:#E1E4E8"> {</span></span>
<span class="line"><span style="color:#79B8FF">  background-color</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">var</span><span style="color:#E1E4E8">(</span><span style="color:#FFAB70">--primary-color</span><span style="color:#E1E4E8">);</span></span>
<span class="line"><span style="color:#79B8FF">  border-radius</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">var</span><span style="color:#E1E4E8">(</span><span style="color:#FFAB70">--border-radius</span><span style="color:#E1E4E8">);</span></span>
<span class="line"><span style="color:#79B8FF">  padding</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">calc</span><span style="color:#E1E4E8">(</span><span style="color:#79B8FF">var</span><span style="color:#E1E4E8">(</span><span style="color:#FFAB70">--spacing-unit</span><span style="color:#E1E4E8">) </span><span style="color:#F97583">*</span><span style="color:#79B8FF"> 0.5</span><span style="color:#E1E4E8">) </span><span style="color:#79B8FF">var</span><span style="color:#E1E4E8">(</span><span style="color:#FFAB70">--spacing-unit</span><span style="color:#E1E4E8">);</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#6A737D">/* Dynamic theming */</span></span>
<span class="line"><span style="color:#E1E4E8">[</span><span style="color:#B392F0">data-theme</span><span style="color:#F97583">=</span><span style="color:#9ECBFF">"dark"</span><span style="color:#E1E4E8">] {</span></span>
<span class="line"><span style="color:#FFAB70">  --primary-color</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">#3b82f6</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#FFAB70">  --secondary-color</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">#94a3b8</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span></code></pre>
<h2 id="container-queries">Container Queries</h2>
<p>Container queries allow components to respond to their container’s size rather than the viewport:</p>
<pre class="astro-code github-dark" style="background-color:#24292e;color:#e1e4e8; overflow-x: auto;" tabindex="0" data-language="css"><code><span class="line"><span style="color:#B392F0">.card-container</span><span style="color:#E1E4E8"> {</span></span>
<span class="line"><span style="color:#79B8FF">  container-type</span><span style="color:#E1E4E8">: inline-size;</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#F97583">@container</span><span style="color:#E1E4E8"> (min-width: 400px) {</span></span>
<span class="line"><span style="color:#B392F0">  .card</span><span style="color:#E1E4E8"> {</span></span>
<span class="line"><span style="color:#79B8FF">    display</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">grid</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#79B8FF">    grid-template-columns</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">1</span><span style="color:#F97583">fr</span><span style="color:#79B8FF"> 2</span><span style="color:#F97583">fr</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#79B8FF">    gap</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">1</span><span style="color:#F97583">rem</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#E1E4E8">  }</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span></code></pre>
<h2 id="css-grid-and-subgrid">CSS Grid and Subgrid</h2>
<p>CSS Grid has matured with powerful features like subgrid:</p>
<pre class="astro-code github-dark" style="background-color:#24292e;color:#e1e4e8; overflow-x: auto;" tabindex="0" data-language="css"><code><span class="line"><span style="color:#B392F0">.layout</span><span style="color:#E1E4E8"> {</span></span>
<span class="line"><span style="color:#79B8FF">  display</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">grid</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#79B8FF">  grid-template-columns</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">repeat</span><span style="color:#E1E4E8">(</span><span style="color:#79B8FF">3</span><span style="color:#E1E4E8">, </span><span style="color:#79B8FF">1</span><span style="color:#F97583">fr</span><span style="color:#E1E4E8">);</span></span>
<span class="line"><span style="color:#79B8FF">  gap</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">2</span><span style="color:#F97583">rem</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#B392F0">.card</span><span style="color:#E1E4E8"> {</span></span>
<span class="line"><span style="color:#79B8FF">  display</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">grid</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#79B8FF">  grid-template-rows</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">subgrid</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#79B8FF">  grid-row</span><span style="color:#E1E4E8">: span </span><span style="color:#79B8FF">3</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span></code></pre>
<h2 id="logical-properties">Logical Properties</h2>
<p>Logical properties make internationalization easier:</p>
<pre class="astro-code github-dark" style="background-color:#24292e;color:#e1e4e8; overflow-x: auto;" tabindex="0" data-language="css"><code><span class="line"><span style="color:#B392F0">.content</span><span style="color:#E1E4E8"> {</span></span>
<span class="line"><span style="color:#6A737D">  /* Instead of margin-left and margin-right */</span></span>
<span class="line"><span style="color:#79B8FF">  margin-inline</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">1</span><span style="color:#F97583">rem</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#E1E4E8">  </span></span>
<span class="line"><span style="color:#6A737D">  /* Instead of padding-top and padding-bottom */</span></span>
<span class="line"><span style="color:#79B8FF">  padding-block</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">2</span><span style="color:#F97583">rem</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#E1E4E8">  </span></span>
<span class="line"><span style="color:#6A737D">  /* Instead of border-left */</span></span>
<span class="line"><span style="color:#79B8FF">  border-inline-start</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">2</span><span style="color:#F97583">px</span><span style="color:#79B8FF"> solid</span><span style="color:#79B8FF"> var</span><span style="color:#E1E4E8">(</span><span style="color:#FFAB70">--primary-color</span><span style="color:#E1E4E8">);</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span></code></pre>
<h2 id="modern-layout-techniques">Modern Layout Techniques</h2>
<h3 id="intrinsic-web-design">Intrinsic Web Design</h3>
<pre class="astro-code github-dark" style="background-color:#24292e;color:#e1e4e8; overflow-x: auto;" tabindex="0" data-language="css"><code><span class="line"><span style="color:#B392F0">.responsive-grid</span><span style="color:#E1E4E8"> {</span></span>
<span class="line"><span style="color:#79B8FF">  display</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">grid</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#79B8FF">  grid-template-columns</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">repeat</span><span style="color:#E1E4E8">(</span><span style="color:#79B8FF">auto-fit</span><span style="color:#E1E4E8">, </span><span style="color:#79B8FF">minmax</span><span style="color:#E1E4E8">(</span><span style="color:#79B8FF">300</span><span style="color:#F97583">px</span><span style="color:#E1E4E8">, </span><span style="color:#79B8FF">1</span><span style="color:#F97583">fr</span><span style="color:#E1E4E8">));</span></span>
<span class="line"><span style="color:#79B8FF">  gap</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">2</span><span style="color:#F97583">rem</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span></code></pre>
<h3 id="aspect-ratio">Aspect Ratio</h3>
<pre class="astro-code github-dark" style="background-color:#24292e;color:#e1e4e8; overflow-x: auto;" tabindex="0" data-language="css"><code><span class="line"><span style="color:#B392F0">.video-container</span><span style="color:#E1E4E8"> {</span></span>
<span class="line"><span style="color:#79B8FF">  aspect-ratio</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">16</span><span style="color:#E1E4E8"> / </span><span style="color:#79B8FF">9</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#79B8FF">  background</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">#000</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#B392F0">.square-image</span><span style="color:#E1E4E8"> {</span></span>
<span class="line"><span style="color:#79B8FF">  aspect-ratio</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">1</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#79B8FF">  object-fit</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">cover</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span></code></pre>
<h2 id="advanced-selectors">Advanced Selectors</h2>
<h3 id="has-pseudo-class">:has() Pseudo-class</h3>
<pre class="astro-code github-dark" style="background-color:#24292e;color:#e1e4e8; overflow-x: auto;" tabindex="0" data-language="css"><code><span class="line"><span style="color:#6A737D">/* Style a card differently if it contains an image */</span></span>
<span class="line"><span style="color:#B392F0">.card:has</span><span style="color:#E1E4E8">(</span><span style="color:#85E89D">img</span><span style="color:#E1E4E8">) {</span></span>
<span class="line"><span style="color:#79B8FF">  grid-template-rows</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">auto</span><span style="color:#79B8FF"> 1</span><span style="color:#F97583">fr</span><span style="color:#79B8FF"> auto</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#6A737D">/* Style a form when it has invalid inputs */</span></span>
<span class="line"><span style="color:#B392F0">.form:has</span><span style="color:#E1E4E8">(</span><span style="color:#B392F0">:invalid</span><span style="color:#E1E4E8">) {</span></span>
<span class="line"><span style="color:#79B8FF">  border-color</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">red</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span></code></pre>
<h3 id="where-and-is">:where() and :is()</h3>
<pre class="astro-code github-dark" style="background-color:#24292e;color:#e1e4e8; overflow-x: auto;" tabindex="0" data-language="css"><code><span class="line"><span style="color:#6A737D">/* Lower specificity with :where() */</span></span>
<span class="line"><span style="color:#B392F0">:where</span><span style="color:#E1E4E8">(</span><span style="color:#85E89D">h1</span><span style="color:#E1E4E8">, </span><span style="color:#85E89D">h2</span><span style="color:#E1E4E8">, </span><span style="color:#85E89D">h3</span><span style="color:#E1E4E8">) {</span></span>
<span class="line"><span style="color:#79B8FF">  margin-block</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">1</span><span style="color:#F97583">em</span><span style="color:#79B8FF"> 0.5</span><span style="color:#F97583">em</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#6A737D">/* Forgiving selector list with :is() */</span></span>
<span class="line"><span style="color:#B392F0">:is</span><span style="color:#E1E4E8">(</span><span style="color:#B392F0">.dark-theme</span><span style="color:#E1E4E8">, </span><span style="color:#B392F0">.high-contrast</span><span style="color:#E1E4E8">) </span><span style="color:#B392F0">.button</span><span style="color:#E1E4E8"> {</span></span>
<span class="line"><span style="color:#79B8FF">  border</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">2</span><span style="color:#F97583">px</span><span style="color:#79B8FF"> solid</span><span style="color:#79B8FF"> currentColor</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span></code></pre>
<h2 id="css-functions">CSS Functions</h2>
<h3 id="clamp">clamp()</h3>
<pre class="astro-code github-dark" style="background-color:#24292e;color:#e1e4e8; overflow-x: auto;" tabindex="0" data-language="css"><code><span class="line"><span style="color:#B392F0">.responsive-text</span><span style="color:#E1E4E8"> {</span></span>
<span class="line"><span style="color:#79B8FF">  font-size</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">clamp</span><span style="color:#E1E4E8">(</span><span style="color:#79B8FF">1</span><span style="color:#F97583">rem</span><span style="color:#E1E4E8">, </span><span style="color:#79B8FF">4</span><span style="color:#F97583">vw</span><span style="color:#E1E4E8">, </span><span style="color:#79B8FF">2</span><span style="color:#F97583">rem</span><span style="color:#E1E4E8">);</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#B392F0">.flexible-width</span><span style="color:#E1E4E8"> {</span></span>
<span class="line"><span style="color:#79B8FF">  width</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">clamp</span><span style="color:#E1E4E8">(</span><span style="color:#79B8FF">300</span><span style="color:#F97583">px</span><span style="color:#E1E4E8">, </span><span style="color:#79B8FF">50</span><span style="color:#F97583">%</span><span style="color:#E1E4E8">, </span><span style="color:#79B8FF">800</span><span style="color:#F97583">px</span><span style="color:#E1E4E8">);</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span></code></pre>
<h3 id="min-and-max">min() and max()</h3>
<pre class="astro-code github-dark" style="background-color:#24292e;color:#e1e4e8; overflow-x: auto;" tabindex="0" data-language="css"><code><span class="line"><span style="color:#B392F0">.container</span><span style="color:#E1E4E8"> {</span></span>
<span class="line"><span style="color:#79B8FF">  width</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">min</span><span style="color:#E1E4E8">(</span><span style="color:#79B8FF">100</span><span style="color:#F97583">%</span><span style="color:#FFAB70"> -</span><span style="color:#79B8FF"> 2</span><span style="color:#F97583">rem</span><span style="color:#E1E4E8">, </span><span style="color:#79B8FF">1200</span><span style="color:#F97583">px</span><span style="color:#E1E4E8">);</span></span>
<span class="line"><span style="color:#79B8FF">  margin-inline</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">auto</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span></code></pre>
<h2 id="color-functions">Color Functions</h2>
<h3 id="color-mix">color-mix()</h3>
<pre class="astro-code github-dark" style="background-color:#24292e;color:#e1e4e8; overflow-x: auto;" tabindex="0" data-language="css"><code><span class="line"><span style="color:#B392F0">.button</span><span style="color:#E1E4E8"> {</span></span>
<span class="line"><span style="color:#79B8FF">  background</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">color-mix</span><span style="color:#E1E4E8">(</span><span style="color:#FFAB70">in</span><span style="color:#79B8FF"> srgb</span><span style="color:#E1E4E8">, </span><span style="color:#79B8FF">var</span><span style="color:#E1E4E8">(</span><span style="color:#FFAB70">--primary-color</span><span style="color:#E1E4E8">) </span><span style="color:#79B8FF">80</span><span style="color:#F97583">%</span><span style="color:#E1E4E8">, </span><span style="color:#79B8FF">white</span><span style="color:#E1E4E8">);</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#B392F0">.hover-state</span><span style="color:#E1E4E8"> {</span></span>
<span class="line"><span style="color:#79B8FF">  background</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">color-mix</span><span style="color:#E1E4E8">(</span><span style="color:#FFAB70">in</span><span style="color:#79B8FF"> srgb</span><span style="color:#E1E4E8">, </span><span style="color:#79B8FF">var</span><span style="color:#E1E4E8">(</span><span style="color:#FFAB70">--primary-color</span><span style="color:#E1E4E8">), </span><span style="color:#79B8FF">black</span><span style="color:#79B8FF"> 10</span><span style="color:#F97583">%</span><span style="color:#E1E4E8">);</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span></code></pre>
<h2 id="performance-optimizations">Performance Optimizations</h2>
<h3 id="content-visibility">content-visibility</h3>
<pre class="astro-code github-dark" style="background-color:#24292e;color:#e1e4e8; overflow-x: auto;" tabindex="0" data-language="css"><code><span class="line"><span style="color:#B392F0">.long-content</span><span style="color:#E1E4E8"> {</span></span>
<span class="line"><span style="color:#79B8FF">  content-visibility</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">auto</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#79B8FF">  contain-intrinsic-size</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">1000</span><span style="color:#F97583">px</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span></code></pre>
<h3 id="will-change">will-change</h3>
<pre class="astro-code github-dark" style="background-color:#24292e;color:#e1e4e8; overflow-x: auto;" tabindex="0" data-language="css"><code><span class="line"><span style="color:#B392F0">.animated-element</span><span style="color:#E1E4E8"> {</span></span>
<span class="line"><span style="color:#79B8FF">  will-change</span><span style="color:#E1E4E8">: transform;</span></span>
<span class="line"><span style="color:#79B8FF">  transition</span><span style="color:#E1E4E8">: transform </span><span style="color:#79B8FF">0.3</span><span style="color:#F97583">s</span><span style="color:#79B8FF"> ease</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#B392F0">.animated-element:hover</span><span style="color:#E1E4E8"> {</span></span>
<span class="line"><span style="color:#79B8FF">  transform</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">translateY</span><span style="color:#E1E4E8">(</span><span style="color:#79B8FF">-4</span><span style="color:#F97583">px</span><span style="color:#E1E4E8">);</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span></code></pre>
<h2 id="best-practices">Best Practices</h2>
<ol>
<li><strong>Use Logical Properties</strong>: Better for internationalization</li>
<li><strong>Leverage Custom Properties</strong>: Easier theming and maintenance</li>
<li><strong>Embrace Container Queries</strong>: More flexible responsive design</li>
<li><strong>Optimize with Modern Functions</strong>: Better performance and flexibility</li>
<li><strong>Progressive Enhancement</strong>: Use feature queries for newer features</li>
</ol>
<pre class="astro-code github-dark" style="background-color:#24292e;color:#e1e4e8; overflow-x: auto;" tabindex="0" data-language="css"><code><span class="line"><span style="color:#F97583">@supports</span><span style="color:#E1E4E8"> (</span><span style="color:#79B8FF">container-type</span><span style="color:#E1E4E8">: inline-size) {</span></span>
<span class="line"><span style="color:#6A737D">  /* Container query styles */</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#F97583">@supports</span><span style="color:#F97583"> not</span><span style="color:#E1E4E8"> (</span><span style="color:#79B8FF">aspect-ratio</span><span style="color:#E1E4E8">: </span><span style="color:#79B8FF">1</span><span style="color:#E1E4E8">) {</span></span>
<span class="line"><span style="color:#6A737D">  /* Fallback styles */</span></span>
<span class="line"><span style="color:#E1E4E8">}</span></span></code></pre>
<h2 id="conclusion">Conclusion</h2>
<p>Modern CSS provides powerful tools for creating maintainable, performant, and flexible stylesheets. By embracing these techniques, you can write cleaner code, create better user experiences, and future-proof your projects.</p>
<p>The key is to gradually adopt these features while maintaining browser support for your target audience. Start with the most widely supported features and progressively enhance with newer capabilities.</p> </div> <!-- Post Footer --> <footer class="post-footer" data-astro-cid-4sn4zg3r> <div class="share-section" data-astro-cid-4sn4zg3r> <h3 data-astro-cid-4sn4zg3r>Share this post</h3> <div class="share-buttons" data-astro-cid-4sn4zg3r> <a href="https://twitter.com/intent/tweet?text=Modern%20CSS%20Techniques%20Every%20Developer%20Should%20Know&url=https%3A%2F%2Fbagusfarisa-astro.vercel.app%2Fblog%2F2024-05-28-modern-css-techniques%2F" target="_blank" rel="noopener noreferrer" class="share-button twitter" data-astro-cid-4sn4zg3r>
Twitter
</a> <a href="https://www.linkedin.com/sharing/share-offsite/?url=https%3A%2F%2Fbagusfarisa-astro.vercel.app%2Fblog%2F2024-05-28-modern-css-techniques%2F" target="_blank" rel="noopener noreferrer" class="share-button linkedin" data-astro-cid-4sn4zg3r>
LinkedIn
</a> </div> </div> </footer> </article>  </main> <footer class="footer" data-astro-cid-sckkx6r4> <div class="footer-container" data-astro-cid-sckkx6r4> <p data-astro-cid-sckkx6r4>&copy; 2024 Guntur. All rights reserved.</p> <div class="social-links" data-astro-cid-sckkx6r4> <a href="https://github.com/bagusfarisa" target="_blank" rel="noopener noreferrer" data-astro-cid-sckkx6r4>GitHub</a> <a href="https://linkedin.com/in/bagusfarisa" target="_blank" rel="noopener noreferrer" data-astro-cid-sckkx6r4>LinkedIn</a> </div> </div> </footer> </body></html> 