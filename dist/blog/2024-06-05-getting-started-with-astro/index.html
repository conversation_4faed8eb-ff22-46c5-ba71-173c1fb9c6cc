<!DOCTYPE html><html lang="en" data-astro-cid-sckkx6r4> <head><meta charset="UTF-8"><meta name="description" content="Learn how to build fast, content-focused websites with Astro framework and why it's becoming a popular choice for developers."><meta name="viewport" content="width=device-width, initial-scale=1.0"><link rel="icon" type="image/svg+xml" href="/favicon.svg"><meta name="generator" content="Astro v5.8.2"><!-- Open Graph / Facebook --><meta property="og:type" content="website"><meta property="og:url" content="https://bagusfarisa-astro.vercel.app/blog/2024-06-05-getting-started-with-astro/"><meta property="og:title" content="Getting Started with Astro: A Modern Static Site Generator - Blog"><meta property="og:description" content="Learn how to build fast, content-focused websites with Astro framework and why it's becoming a popular choice for developers."><meta property="og:image" content="https://bagusfarisa-astro.vercel.app/images/astro-blog-cover.jpg"><!-- Twitter --><meta property="twitter:card" content="summary_large_image"><meta property="twitter:url" content="https://bagusfarisa-astro.vercel.app/blog/2024-06-05-getting-started-with-astro/"><meta property="twitter:title" content="Getting Started with Astro: A Modern Static Site Generator - Blog"><meta property="twitter:description" content="Learn how to build fast, content-focused websites with Astro framework and why it's becoming a popular choice for developers."><meta property="twitter:image" content="https://bagusfarisa-astro.vercel.app/images/astro-blog-cover.jpg"><title>Getting Started with Astro: A Modern Static Site Generator - Blog</title><style>:root{--primary-color: #2563eb;--secondary-color: #64748b;--text-color: #1e293b;--bg-color: #ffffff;--border-color: #e2e8f0;--hover-color: #f8fafc}[data-astro-cid-sckkx6r4]{margin:0;padding:0;box-sizing:border-box}body{font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,sans-serif;line-height:1.6;color:var(--text-color);background-color:var(--bg-color)}.navbar[data-astro-cid-sckkx6r4]{background:var(--bg-color);border-bottom:1px solid var(--border-color);position:sticky;top:0;z-index:100}.nav-container[data-astro-cid-sckkx6r4]{max-width:1200px;margin:0 auto;padding:1rem 2rem;display:flex;justify-content:space-between;align-items:center}.nav-logo[data-astro-cid-sckkx6r4]{font-size:1.5rem;font-weight:700;color:var(--primary-color);text-decoration:none}.nav-menu[data-astro-cid-sckkx6r4]{display:flex;list-style:none;gap:2rem}.nav-link[data-astro-cid-sckkx6r4]{color:var(--text-color);text-decoration:none;font-weight:500;transition:color .3s ease}.nav-link[data-astro-cid-sckkx6r4]:hover{color:var(--primary-color)}main[data-astro-cid-sckkx6r4]{min-height:calc(100vh - 140px);max-width:1200px;margin:0 auto;padding:2rem}.footer[data-astro-cid-sckkx6r4]{background:var(--hover-color);border-top:1px solid var(--border-color);margin-top:4rem}.footer-container[data-astro-cid-sckkx6r4]{max-width:1200px;margin:0 auto;padding:2rem;display:flex;justify-content:space-between;align-items:center;flex-wrap:wrap;gap:1rem}.social-links[data-astro-cid-sckkx6r4]{display:flex;gap:1rem}.social-links[data-astro-cid-sckkx6r4] a[data-astro-cid-sckkx6r4]{color:var(--secondary-color);text-decoration:none;transition:color .3s ease}.social-links[data-astro-cid-sckkx6r4] a[data-astro-cid-sckkx6r4]:hover{color:var(--primary-color)}@media (max-width: 768px){.nav-container[data-astro-cid-sckkx6r4]{padding:1rem}.nav-menu[data-astro-cid-sckkx6r4]{gap:1rem}main[data-astro-cid-sckkx6r4]{padding:1rem}.footer-container[data-astro-cid-sckkx6r4]{flex-direction:column;text-align:center}}
.blog-post[data-astro-cid-4sn4zg3r]{max-width:800px;margin:0 auto}.breadcrumb[data-astro-cid-4sn4zg3r]{margin-bottom:2rem}.back-link[data-astro-cid-4sn4zg3r]{color:var(--primary-color);text-decoration:none;font-weight:500;transition:color .3s ease}.back-link[data-astro-cid-4sn4zg3r]:hover{color:#1d4ed8}.post-header[data-astro-cid-4sn4zg3r]{margin-bottom:3rem}.post-title[data-astro-cid-4sn4zg3r]{font-size:3rem;font-weight:700;margin-bottom:1.5rem;color:var(--text-color);line-height:1.2}.post-meta[data-astro-cid-4sn4zg3r]{display:flex;gap:2rem;margin-bottom:1.5rem;flex-wrap:wrap;color:var(--secondary-color);font-size:.875rem}.meta-item[data-astro-cid-4sn4zg3r]{display:flex;align-items:center;gap:.5rem}.meta-label[data-astro-cid-4sn4zg3r]{font-weight:600;color:var(--text-color)}.post-description[data-astro-cid-4sn4zg3r]{font-size:1.25rem;color:var(--secondary-color);margin-bottom:2rem;line-height:1.6;font-style:italic}.post-tags[data-astro-cid-4sn4zg3r]{display:flex;align-items:center;gap:1rem;flex-wrap:wrap}.tags-label[data-astro-cid-4sn4zg3r]{font-weight:600;color:var(--text-color)}.tags-list[data-astro-cid-4sn4zg3r]{display:flex;flex-wrap:wrap;gap:.5rem}.tag[data-astro-cid-4sn4zg3r]{background:var(--hover-color);color:var(--secondary-color);padding:.25rem .75rem;border-radius:20px;font-size:.875rem;font-weight:500}.post-image[data-astro-cid-4sn4zg3r]{margin-bottom:3rem;border-radius:12px;overflow:hidden;box-shadow:0 10px 25px -3px #0000001a}.post-image[data-astro-cid-4sn4zg3r] img[data-astro-cid-4sn4zg3r]{width:100%;height:auto;display:block}.post-content[data-astro-cid-4sn4zg3r]{line-height:1.7;color:var(--text-color);margin-bottom:3rem}.post-content[data-astro-cid-4sn4zg3r] h2{margin-top:2.5rem;margin-bottom:1rem;font-weight:600;font-size:1.875rem}.post-content[data-astro-cid-4sn4zg3r] h3{margin-top:2rem;margin-bottom:.75rem;font-weight:600;font-size:1.5rem}.post-content[data-astro-cid-4sn4zg3r] h4{margin-top:1.5rem;margin-bottom:.5rem;font-weight:600;font-size:1.25rem}.post-content[data-astro-cid-4sn4zg3r] p{margin-bottom:1.5rem}.post-content[data-astro-cid-4sn4zg3r] ul,.post-content[data-astro-cid-4sn4zg3r] ol{margin-bottom:1.5rem;padding-left:1.5rem}.post-content[data-astro-cid-4sn4zg3r] li{margin-bottom:.5rem}.post-content[data-astro-cid-4sn4zg3r] blockquote{border-left:4px solid var(--primary-color);padding-left:1rem;margin:1.5rem 0;font-style:italic;color:var(--secondary-color)}.post-content[data-astro-cid-4sn4zg3r] code{background:var(--hover-color);padding:.25rem .5rem;border-radius:4px;font-family:Monaco,Menlo,monospace;font-size:.875rem}.post-content[data-astro-cid-4sn4zg3r] pre{background:var(--hover-color);padding:1.5rem;border-radius:8px;overflow-x:auto;margin-bottom:1.5rem}.post-content[data-astro-cid-4sn4zg3r] pre code{background:none;padding:0}.post-content[data-astro-cid-4sn4zg3r] img{max-width:100%;height:auto;border-radius:8px;margin:1.5rem 0}.post-footer[data-astro-cid-4sn4zg3r]{border-top:1px solid var(--border-color);padding-top:2rem}.share-section[data-astro-cid-4sn4zg3r] h3[data-astro-cid-4sn4zg3r]{margin-bottom:1rem;color:var(--text-color);font-weight:600}.share-buttons[data-astro-cid-4sn4zg3r]{display:flex;gap:1rem}.share-button[data-astro-cid-4sn4zg3r]{padding:.5rem 1rem;border-radius:6px;text-decoration:none;font-weight:500;transition:all .3s ease;font-size:.875rem}.share-button[data-astro-cid-4sn4zg3r].twitter{background:#1da1f2;color:#fff}.share-button[data-astro-cid-4sn4zg3r].twitter:hover{background:#1a91da}.share-button[data-astro-cid-4sn4zg3r].linkedin{background:#0077b5;color:#fff}.share-button[data-astro-cid-4sn4zg3r].linkedin:hover{background:#006ba1}@media (max-width: 768px){.post-title[data-astro-cid-4sn4zg3r]{font-size:2.5rem}.post-description[data-astro-cid-4sn4zg3r]{font-size:1.125rem}.post-meta[data-astro-cid-4sn4zg3r]{flex-direction:column;gap:.5rem}.post-tags[data-astro-cid-4sn4zg3r]{flex-direction:column;align-items:flex-start;gap:.75rem}.share-buttons[data-astro-cid-4sn4zg3r]{flex-direction:column}.share-button[data-astro-cid-4sn4zg3r]{text-align:center}}
</style></head> <body data-astro-cid-sckkx6r4> <nav class="navbar" data-astro-cid-sckkx6r4> <div class="nav-container" data-astro-cid-sckkx6r4> <a href="/" class="nav-logo" data-astro-cid-sckkx6r4>Guntur</a> <ul class="nav-menu" data-astro-cid-sckkx6r4> <li class="nav-item" data-astro-cid-sckkx6r4> <a href="/" class="nav-link" data-astro-cid-sckkx6r4>Home</a> </li> <li class="nav-item" data-astro-cid-sckkx6r4> <a href="/projects" class="nav-link" data-astro-cid-sckkx6r4>Projects</a> </li> <li class="nav-item" data-astro-cid-sckkx6r4> <a href="/blog" class="nav-link" data-astro-cid-sckkx6r4>Blog</a> </li> </ul> </div> </nav> <main data-astro-cid-sckkx6r4>  <article class="blog-post" data-astro-cid-4sn4zg3r> <!-- Back Navigation --> <nav class="breadcrumb" data-astro-cid-4sn4zg3r> <a href="/blog" class="back-link" data-astro-cid-4sn4zg3r>← Back to Blog</a> </nav> <!-- Post Header --> <header class="post-header" data-astro-cid-4sn4zg3r> <h1 class="post-title" data-astro-cid-4sn4zg3r>Getting Started with Astro: A Modern Static Site Generator</h1> <div class="post-meta" data-astro-cid-4sn4zg3r> <div class="meta-item" data-astro-cid-4sn4zg3r> <span class="meta-label" data-astro-cid-4sn4zg3r>Published:</span> <time datetime="2024-06-05T09:00:00.000Z" data-astro-cid-4sn4zg3r> June 5, 2024 </time> </div>  <div class="meta-item" data-astro-cid-4sn4zg3r> <span class="meta-label" data-astro-cid-4sn4zg3r>Author:</span> <span data-astro-cid-4sn4zg3r>Guntur</span> </div> </div> <p class="post-description" data-astro-cid-4sn4zg3r>Learn how to build fast, content-focused websites with Astro framework and why it&#39;s becoming a popular choice for developers.</p> <!-- Tags --> <div class="post-tags" data-astro-cid-4sn4zg3r> <span class="tags-label" data-astro-cid-4sn4zg3r>Tags:</span> <div class="tags-list" data-astro-cid-4sn4zg3r> <span class="tag" data-astro-cid-4sn4zg3r>Astro</span><span class="tag" data-astro-cid-4sn4zg3r>Web Development</span><span class="tag" data-astro-cid-4sn4zg3r>Static Sites</span><span class="tag" data-astro-cid-4sn4zg3r>JavaScript</span> </div> </div> </header> <!-- Featured Image --> <div class="post-image" data-astro-cid-4sn4zg3r> <img src="/images/astro-blog-cover.jpg" alt="Getting Started with Astro: A Modern Static Site Generator" data-astro-cid-4sn4zg3r> </div> <!-- Post Content --> <div class="post-content" data-astro-cid-4sn4zg3r> <h1 id="getting-started-with-astro-a-modern-static-site-generator">Getting Started with Astro: A Modern Static Site Generator</h1>
<p>Astro has been gaining significant traction in the web development community, and for good reason. It’s a modern static site generator that prioritizes performance and developer experience while offering unique features that set it apart from other frameworks.</p>
<h2 id="what-makes-astro-special">What Makes Astro Special?</h2>
<h3 id="zero-javascript-by-default">Zero JavaScript by Default</h3>
<p>One of Astro’s most compelling features is its “zero JavaScript by default” approach. Unlike other frameworks that ship JavaScript to the browser regardless of whether it’s needed, Astro only includes JavaScript when you explicitly request it.</p>
<pre class="astro-code github-dark" style="background-color:#24292e;color:#e1e4e8; overflow-x: auto;" tabindex="0" data-language="astro"><code><span class="line"><span style="color:#6A737D">---</span></span>
<span class="line"><span style="color:#6A737D">// This runs on the server, not in the browser</span></span>
<span class="line"><span style="color:#F97583">const</span><span style="color:#79B8FF"> data</span><span style="color:#F97583"> =</span><span style="color:#F97583"> await</span><span style="color:#B392F0"> fetch</span><span style="color:#E1E4E8">(</span><span style="color:#9ECBFF">'https://api.example.com/data'</span><span style="color:#E1E4E8">);</span></span>
<span class="line"><span style="color:#6A737D">---</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E1E4E8">&#x3C;</span><span style="color:#85E89D">div</span><span style="color:#E1E4E8">></span></span>
<span class="line"><span style="color:#6A737D">  &#x3C;!-- This is just HTML, no JavaScript needed --></span></span>
<span class="line"><span style="color:#E1E4E8">  &#x3C;</span><span style="color:#85E89D">h1</span><span style="color:#E1E4E8">>Welcome to my site&#x3C;/</span><span style="color:#85E89D">h1</span><span style="color:#E1E4E8">></span></span>
<span class="line"><span style="color:#E1E4E8">  &#x3C;</span><span style="color:#85E89D">p</span><span style="color:#E1E4E8">>Data loaded: {data.length} items&#x3C;/</span><span style="color:#85E89D">p</span><span style="color:#E1E4E8">></span></span>
<span class="line"><span style="color:#E1E4E8">&#x3C;/</span><span style="color:#85E89D">div</span><span style="color:#E1E4E8">></span></span></code></pre>
<h3 id="component-islands-architecture">Component Islands Architecture</h3>
<p>Astro introduces the concept of “islands” - interactive components that are hydrated independently. This means you can have a mostly static page with small interactive elements without shipping JavaScript for the entire page.</p>
<pre class="astro-code github-dark" style="background-color:#24292e;color:#e1e4e8; overflow-x: auto;" tabindex="0" data-language="astro"><code><span class="line"><span style="color:#6A737D">---</span></span>
<span class="line"><span style="color:#F97583">import</span><span style="color:#E1E4E8"> InteractiveCounter </span><span style="color:#F97583">from</span><span style="color:#9ECBFF"> './InteractiveCounter.jsx'</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#F97583">import</span><span style="color:#E1E4E8"> StaticHeader </span><span style="color:#F97583">from</span><span style="color:#9ECBFF"> './StaticHeader.astro'</span><span style="color:#E1E4E8">;</span></span>
<span class="line"><span style="color:#6A737D">---</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E1E4E8">&#x3C;</span><span style="color:#79B8FF">StaticHeader</span><span style="color:#E1E4E8"> /></span></span>
<span class="line"><span style="color:#6A737D">&#x3C;!-- This component will be hydrated --></span></span>
<span class="line"><span style="color:#E1E4E8">&#x3C;</span><span style="color:#79B8FF">InteractiveCounter</span><span style="color:#B392F0"> client:load</span><span style="color:#E1E4E8"> /></span></span>
<span class="line"><span style="color:#6A737D">&#x3C;!-- Rest of the page remains static --></span></span>
<span class="line"><span style="color:#E1E4E8">&#x3C;</span><span style="color:#85E89D">footer</span><span style="color:#E1E4E8">>Static footer content&#x3C;/</span><span style="color:#85E89D">footer</span><span style="color:#E1E4E8">></span></span></code></pre>
<h2 id="key-benefits">Key Benefits</h2>
<h3 id="performance">Performance</h3>
<ul>
<li><strong>Faster Load Times</strong>: Minimal JavaScript means faster initial page loads</li>
<li><strong>Better Core Web Vitals</strong>: Optimized for Google’s performance metrics</li>
<li><strong>Efficient Bundling</strong>: Only ships the JavaScript you actually need</li>
</ul>
<h3 id="developer-experience">Developer Experience</h3>
<ul>
<li><strong>Framework Agnostic</strong>: Use React, Vue, Svelte, or any framework you prefer</li>
<li><strong>TypeScript Support</strong>: Built-in TypeScript support without configuration</li>
<li><strong>Hot Module Replacement</strong>: Fast development with instant updates</li>
</ul>
<h3 id="seo-and-accessibility">SEO and Accessibility</h3>
<ul>
<li><strong>Server-Side Rendering</strong>: Content is rendered on the server for better SEO</li>
<li><strong>Static HTML</strong>: Search engines can easily crawl and index your content</li>
<li><strong>Progressive Enhancement</strong>: Works even when JavaScript is disabled</li>
</ul>
<h2 id="getting-started">Getting Started</h2>
<h3 id="installation">Installation</h3>
<pre class="astro-code github-dark" style="background-color:#24292e;color:#e1e4e8; overflow-x: auto;" tabindex="0" data-language="bash"><code><span class="line"><span style="color:#B392F0">npm</span><span style="color:#9ECBFF"> create</span><span style="color:#9ECBFF"> astro@latest</span><span style="color:#9ECBFF"> my-astro-site</span></span>
<span class="line"><span style="color:#79B8FF">cd</span><span style="color:#9ECBFF"> my-astro-site</span></span>
<span class="line"><span style="color:#B392F0">npm</span><span style="color:#9ECBFF"> install</span></span>
<span class="line"><span style="color:#B392F0">npm</span><span style="color:#9ECBFF"> run</span><span style="color:#9ECBFF"> dev</span></span></code></pre>
<h3 id="project-structure">Project Structure</h3>
<pre class="astro-code github-dark" style="background-color:#24292e;color:#e1e4e8; overflow-x: auto;" tabindex="0" data-language="plaintext"><code><span class="line"><span>src/</span></span>
<span class="line"><span>├── components/     # Reusable components</span></span>
<span class="line"><span>├── layouts/        # Page layouts</span></span>
<span class="line"><span>├── pages/          # File-based routing</span></span>
<span class="line"><span>└── content/        # Content collections</span></span></code></pre>
<h3 id="content-collections">Content Collections</h3>
<p>Astro’s content collections provide type-safe content management:</p>
<pre class="astro-code github-dark" style="background-color:#24292e;color:#e1e4e8; overflow-x: auto;" tabindex="0" data-language="typescript"><code><span class="line"><span style="color:#6A737D">// src/content/config.ts</span></span>
<span class="line"><span style="color:#F97583">import</span><span style="color:#E1E4E8"> { defineCollection, z } </span><span style="color:#F97583">from</span><span style="color:#9ECBFF"> 'astro:content'</span><span style="color:#E1E4E8">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#F97583">const</span><span style="color:#79B8FF"> blogCollection</span><span style="color:#F97583"> =</span><span style="color:#B392F0"> defineCollection</span><span style="color:#E1E4E8">({</span></span>
<span class="line"><span style="color:#E1E4E8">  type: </span><span style="color:#9ECBFF">'content'</span><span style="color:#E1E4E8">,</span></span>
<span class="line"><span style="color:#E1E4E8">  schema: z.</span><span style="color:#B392F0">object</span><span style="color:#E1E4E8">({</span></span>
<span class="line"><span style="color:#E1E4E8">    title: z.</span><span style="color:#B392F0">string</span><span style="color:#E1E4E8">(),</span></span>
<span class="line"><span style="color:#E1E4E8">    publishDate: z.</span><span style="color:#B392F0">date</span><span style="color:#E1E4E8">(),</span></span>
<span class="line"><span style="color:#E1E4E8">    tags: z.</span><span style="color:#B392F0">array</span><span style="color:#E1E4E8">(z.</span><span style="color:#B392F0">string</span><span style="color:#E1E4E8">()),</span></span>
<span class="line"><span style="color:#E1E4E8">  }),</span></span>
<span class="line"><span style="color:#E1E4E8">});</span></span>
<span class="line"></span>
<span class="line"><span style="color:#F97583">export</span><span style="color:#F97583"> const</span><span style="color:#79B8FF"> collections</span><span style="color:#F97583"> =</span><span style="color:#E1E4E8"> {</span></span>
<span class="line"><span style="color:#9ECBFF">  'blog'</span><span style="color:#E1E4E8">: blogCollection,</span></span>
<span class="line"><span style="color:#E1E4E8">};</span></span></code></pre>
<h2 id="when-to-choose-astro">When to Choose Astro</h2>
<p>Astro is particularly well-suited for:</p>
<ul>
<li><strong>Content-focused sites</strong>: Blogs, documentation, marketing sites</li>
<li><strong>E-commerce sites</strong>: Product catalogs, landing pages</li>
<li><strong>Portfolio sites</strong>: Showcasing work with minimal interactivity</li>
<li><strong>Corporate websites</strong>: Company sites with occasional interactive elements</li>
</ul>
<h2 id="conclusion">Conclusion</h2>
<p>Astro represents a thoughtful approach to modern web development, prioritizing performance without sacrificing developer experience. Its unique architecture makes it an excellent choice for content-focused websites that need to be fast, SEO-friendly, and maintainable.</p>
<p>Whether you’re building a personal blog, a company website, or a documentation site, Astro provides the tools and performance characteristics to create exceptional web experiences.</p>
<p>Ready to give Astro a try? Start with their excellent documentation and join the growing community of developers who are building faster websites with less JavaScript.</p> </div> <!-- Post Footer --> <footer class="post-footer" data-astro-cid-4sn4zg3r> <div class="share-section" data-astro-cid-4sn4zg3r> <h3 data-astro-cid-4sn4zg3r>Share this post</h3> <div class="share-buttons" data-astro-cid-4sn4zg3r> <a href="https://twitter.com/intent/tweet?text=Getting%20Started%20with%20Astro%3A%20A%20Modern%20Static%20Site%20Generator&url=https%3A%2F%2Fbagusfarisa-astro.vercel.app%2Fblog%2F2024-06-05-getting-started-with-astro%2F" target="_blank" rel="noopener noreferrer" class="share-button twitter" data-astro-cid-4sn4zg3r>
Twitter
</a> <a href="https://www.linkedin.com/sharing/share-offsite/?url=https%3A%2F%2Fbagusfarisa-astro.vercel.app%2Fblog%2F2024-06-05-getting-started-with-astro%2F" target="_blank" rel="noopener noreferrer" class="share-button linkedin" data-astro-cid-4sn4zg3r>
LinkedIn
</a> </div> </div> </footer> </article>  </main> <footer class="footer" data-astro-cid-sckkx6r4> <div class="footer-container" data-astro-cid-sckkx6r4> <p data-astro-cid-sckkx6r4>&copy; 2024 Guntur. All rights reserved.</p> <div class="social-links" data-astro-cid-sckkx6r4> <a href="https://github.com/bagusfarisa" target="_blank" rel="noopener noreferrer" data-astro-cid-sckkx6r4>GitHub</a> <a href="https://linkedin.com/in/bagusfarisa" target="_blank" rel="noopener noreferrer" data-astro-cid-sckkx6r4>LinkedIn</a> </div> </div> </footer> </body></html> 