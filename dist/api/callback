<!DOCTYPE html>
<html>
<head>
  <title>Authorizing...</title>
  <meta name="robots" content="noindex">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      margin: 0;
      background: #f5f5f5;
    }
    .container {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      text-align: center;
      max-width: 400px;
    }
    .spinner {
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 2s linear infinite;
      margin: 0 auto 1rem;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .error {
      color: #dc3545;
    }
    .success {
      color: #28a745;
    }
  </style>
</head>
<body>
  <div class="container">
    <h2 class="error">Authentication Failed</h2><p>There was an error during authentication.</p>
  </div>

  <script>
    (function() {
      function receiveMessage(e) {
        console.log("receiveMessage %o", e);
        
      // Send error message to parent
      e.source.postMessage(
        "authorization:github:error:" + JSON.stringify({
          error: "No authorization code received"
        }),
        e.origin
      );
      }

      window.addEventListener("message", receiveMessage, false);

      // Also try to send the message immediately in case the parent is ready
      if (window.opener) {
        
      window.opener.postMessage(
        "authorization:github:error:" + JSON.stringify({
          error: "No authorization code received"
        }),
        window.location.origin
      );
      }
    })();
  </script>
</body>
</html>