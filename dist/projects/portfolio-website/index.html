<!DOCTYPE html><html lang="en" data-astro-cid-sckkx6r4> <head><meta charset="UTF-8"><meta name="description" content="A modern portfolio website built with Astro framework and integrated with Decap CMS for easy content management."><meta name="viewport" content="width=device-width, initial-scale=1.0"><link rel="icon" type="image/svg+xml" href="/favicon.svg"><meta name="generator" content="Astro v5.8.2"><!-- Open Graph / Facebook --><meta property="og:type" content="website"><meta property="og:url" content="https://bagusfarisa-astro.vercel.app/projects/portfolio-website/"><meta property="og:title" content="Portfolio Website with Astro &#38; Decap CMS - Projects"><meta property="og:description" content="A modern portfolio website built with Astro framework and integrated with Decap CMS for easy content management."><meta property="og:image" content="https://bagusfarisa-astro.vercel.app/images/portfolio-preview.jpg"><!-- Twitter --><meta property="twitter:card" content="summary_large_image"><meta property="twitter:url" content="https://bagusfarisa-astro.vercel.app/projects/portfolio-website/"><meta property="twitter:title" content="Portfolio Website with Astro &#38; Decap CMS - Projects"><meta property="twitter:description" content="A modern portfolio website built with Astro framework and integrated with Decap CMS for easy content management."><meta property="twitter:image" content="https://bagusfarisa-astro.vercel.app/images/portfolio-preview.jpg"><title>Portfolio Website with Astro &amp; Decap CMS - Projects</title><style>:root{--primary-color: #2563eb;--secondary-color: #64748b;--text-color: #1e293b;--bg-color: #ffffff;--border-color: #e2e8f0;--hover-color: #f8fafc}[data-astro-cid-sckkx6r4]{margin:0;padding:0;box-sizing:border-box}body{font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,sans-serif;line-height:1.6;color:var(--text-color);background-color:var(--bg-color)}.navbar[data-astro-cid-sckkx6r4]{background:var(--bg-color);border-bottom:1px solid var(--border-color);position:sticky;top:0;z-index:100}.nav-container[data-astro-cid-sckkx6r4]{max-width:1200px;margin:0 auto;padding:1rem 2rem;display:flex;justify-content:space-between;align-items:center}.nav-logo[data-astro-cid-sckkx6r4]{font-size:1.5rem;font-weight:700;color:var(--primary-color);text-decoration:none}.nav-menu[data-astro-cid-sckkx6r4]{display:flex;list-style:none;gap:2rem}.nav-link[data-astro-cid-sckkx6r4]{color:var(--text-color);text-decoration:none;font-weight:500;transition:color .3s ease}.nav-link[data-astro-cid-sckkx6r4]:hover{color:var(--primary-color)}main[data-astro-cid-sckkx6r4]{min-height:calc(100vh - 140px);max-width:1200px;margin:0 auto;padding:2rem}.footer[data-astro-cid-sckkx6r4]{background:var(--hover-color);border-top:1px solid var(--border-color);margin-top:4rem}.footer-container[data-astro-cid-sckkx6r4]{max-width:1200px;margin:0 auto;padding:2rem;display:flex;justify-content:space-between;align-items:center;flex-wrap:wrap;gap:1rem}.social-links[data-astro-cid-sckkx6r4]{display:flex;gap:1rem}.social-links[data-astro-cid-sckkx6r4] a[data-astro-cid-sckkx6r4]{color:var(--secondary-color);text-decoration:none;transition:color .3s ease}.social-links[data-astro-cid-sckkx6r4] a[data-astro-cid-sckkx6r4]:hover{color:var(--primary-color)}@media (max-width: 768px){.nav-container[data-astro-cid-sckkx6r4]{padding:1rem}.nav-menu[data-astro-cid-sckkx6r4]{gap:1rem}main[data-astro-cid-sckkx6r4]{padding:1rem}.footer-container[data-astro-cid-sckkx6r4]{flex-direction:column;text-align:center}}
</style>
<link rel="stylesheet" href="/_astro/_slug_.DSYtqgOx.css"></head> <body data-astro-cid-sckkx6r4> <nav class="navbar" data-astro-cid-sckkx6r4> <div class="nav-container" data-astro-cid-sckkx6r4> <a href="/" class="nav-logo" data-astro-cid-sckkx6r4>Guntur</a> <ul class="nav-menu" data-astro-cid-sckkx6r4> <li class="nav-item" data-astro-cid-sckkx6r4> <a href="/" class="nav-link" data-astro-cid-sckkx6r4>Home</a> </li> <li class="nav-item" data-astro-cid-sckkx6r4> <a href="/projects" class="nav-link" data-astro-cid-sckkx6r4>Projects</a> </li> <li class="nav-item" data-astro-cid-sckkx6r4> <a href="/blog" class="nav-link" data-astro-cid-sckkx6r4>Blog</a> </li> </ul> </div> </nav> <main data-astro-cid-sckkx6r4>  <article class="project-detail" data-astro-cid-ovxcmftc> <!-- Back Navigation --> <nav class="breadcrumb" data-astro-cid-ovxcmftc> <a href="/projects" class="back-link" data-astro-cid-ovxcmftc>← Back to Projects</a> </nav> <!-- Project Header --> <header class="project-header" data-astro-cid-ovxcmftc> <h1 class="project-title" data-astro-cid-ovxcmftc>Portfolio Website with Astro &amp; Decap CMS</h1> <p class="project-description" data-astro-cid-ovxcmftc>A modern portfolio website built with Astro framework and integrated with Decap CMS for easy content management.</p> <div class="project-meta" data-astro-cid-ovxcmftc> <div class="meta-item" data-astro-cid-ovxcmftc> <span class="meta-label" data-astro-cid-ovxcmftc>Published:</span> <time datetime="2024-06-05T10:00:00.000Z" data-astro-cid-ovxcmftc> June 5, 2024 </time> </div> <div class="meta-item" data-astro-cid-ovxcmftc> <span class="meta-label" data-astro-cid-ovxcmftc>Status:</span> <span class="status status-completed" data-astro-cid-ovxcmftc> Completed </span> </div> </div> <!-- Technologies --> <div class="technologies" data-astro-cid-ovxcmftc> <h3 data-astro-cid-ovxcmftc>Technologies Used:</h3> <div class="tech-list" data-astro-cid-ovxcmftc> <span class="tech-tag" data-astro-cid-ovxcmftc>Astro</span><span class="tech-tag" data-astro-cid-ovxcmftc>TypeScript</span><span class="tech-tag" data-astro-cid-ovxcmftc>Decap CMS</span><span class="tech-tag" data-astro-cid-ovxcmftc>CSS</span><span class="tech-tag" data-astro-cid-ovxcmftc>Vercel</span> </div> </div> <!-- Project Links --> <div class="project-links" data-astro-cid-ovxcmftc> <a href="https://bagusfarisa-astro.vercel.app" target="_blank" rel="noopener noreferrer" class="project-link live" data-astro-cid-ovxcmftc>
🚀 Live Demo
</a> <a href="https://github.com/bagusfarisa/bagusfarisa-astro" target="_blank" rel="noopener noreferrer" class="project-link github" data-astro-cid-ovxcmftc>
📂 GitHub Repository
</a> </div> </header> <!-- Project Image --> <div class="project-image" data-astro-cid-ovxcmftc> <img src="/images/portfolio-preview.jpg" alt="Portfolio Website with Astro &#38; Decap CMS" data-astro-cid-ovxcmftc> </div> <!-- Project Gallery -->  <!-- Project Content --> <div class="project-content" data-astro-cid-ovxcmftc> <h1 id="portfolio-website-with-astro--decap-cms">Portfolio Website with Astro &#x26; Decap CMS</h1>
<p>This portfolio website showcases my projects and blog posts, built with modern web technologies for optimal performance and easy content management.</p>
<h2 id="features">Features</h2>
<ul>
<li><strong>Static Site Generation</strong>: Built with Astro for lightning-fast performance</li>
<li><strong>Content Management</strong>: Integrated with Decap CMS for easy content editing</li>
<li><strong>Responsive Design</strong>: Mobile-first approach ensuring great experience on all devices</li>
<li><strong>SEO Optimized</strong>: Proper meta tags, structured data, and performance optimization</li>
<li><strong>Modern Stack</strong>: TypeScript, CSS custom properties, and modern JavaScript</li>
</ul>
<h2 id="technical-implementation">Technical Implementation</h2>
<h3 id="astro-framework">Astro Framework</h3>
<p>The site leverages Astro’s unique approach to static site generation, allowing for:</p>
<ul>
<li>Zero JavaScript by default</li>
<li>Component-based architecture</li>
<li>Content collections for type-safe content management</li>
<li>Excellent performance out of the box</li>
</ul>
<h3 id="decap-cms-integration">Decap CMS Integration</h3>
<p>Content management is handled through Decap CMS, providing:</p>
<ul>
<li>Git-based workflow</li>
<li>Rich text editing</li>
<li>Media management</li>
<li>Preview functionality</li>
<li>User-friendly interface for non-technical users</li>
</ul>
<h3 id="deployment">Deployment</h3>
<p>The site is deployed on Vercel with:</p>
<ul>
<li>Automatic deployments from Git</li>
<li>Edge network distribution</li>
<li>Optimized build process</li>
<li>Environment variable management</li>
</ul>
<h2 id="development-process">Development Process</h2>
<ol>
<li><strong>Planning</strong>: Defined the site structure and content requirements</li>
<li><strong>Design</strong>: Created a clean, professional design system</li>
<li><strong>Development</strong>: Built components and pages with Astro</li>
<li><strong>CMS Setup</strong>: Configured Decap CMS for content management</li>
<li><strong>Testing</strong>: Ensured cross-browser compatibility and performance</li>
<li><strong>Deployment</strong>: Set up CI/CD pipeline with Vercel</li>
</ol>
<p>This project demonstrates my ability to work with modern web technologies while maintaining focus on performance, accessibility, and user experience.</p> </div> </article>  </main> <footer class="footer" data-astro-cid-sckkx6r4> <div class="footer-container" data-astro-cid-sckkx6r4> <p data-astro-cid-sckkx6r4>&copy; 2024 Guntur. All rights reserved.</p> <div class="social-links" data-astro-cid-sckkx6r4> <a href="https://github.com/bagusfarisa" target="_blank" rel="noopener noreferrer" data-astro-cid-sckkx6r4>GitHub</a> <a href="https://linkedin.com/in/bagusfarisa" target="_blank" rel="noopener noreferrer" data-astro-cid-sckkx6r4>LinkedIn</a> </div> </div> </footer> </body></html> 