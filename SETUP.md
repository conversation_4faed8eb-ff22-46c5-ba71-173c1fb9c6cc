# Portfolio Website Setup Guide

This guide will help you set up and deploy your Astro portfolio website with Decap CMS integration.

## 🚀 Quick Start

### 1. Development Setup

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Open in browser
open http://localhost:4321
```

### 2. Content Management

#### Access CMS Admin Interface
- **Local Development**: http://localhost:4321/admin or http://localhost:4321/admin/
- **Production**: https://your-domain.com/admin or https://your-domain.com/admin/

#### CMS Authentication Setup

For production deployment, you'll need to set up authentication:

**Option 1: Netlify Identity (Recommended)**
1. Deploy to Netlify or use Netlify Identity with other hosts
2. Enable Netlify Identity in your Netlify dashboard
3. Configure Git Gateway in Netlify Identity settings
4. Invite users through the Netlify dashboard

**Option 2: GitHub OAuth**
1. Create a GitHub OAuth App in your GitHub settings
2. Update `public/admin/config.yml`:
```yaml
backend:
  name: github
  repo: your-username/your-repo-name
  branch: main
```

### 3. Content Creation

#### Adding Projects
1. Go to `/admin/` and log in
2. Navigate to "Projects" collection
3. Click "New Project"
4. Fill in the required fields:
   - Title
   - Description
   - Technologies (add multiple)
   - URLs (optional)
   - Featured status
   - Publish date
   - Content body

#### Adding Blog Posts
1. Go to `/admin/` and log in
2. Navigate to "Blog" collection
3. Click "New Blog"
4. Fill in the required fields:
   - Title
   - Description
   - Author
   - Tags (add multiple)
   - Featured status
   - Draft status
   - Content body

#### Manual Content Creation
You can also create content manually by adding Markdown files:

**Projects**: `src/content/projects/your-project.md`
```markdown
---
title: "Your Project Title"
description: "Brief description"
technologies: ["React", "Node.js", "MongoDB"]
liveUrl: "https://your-project.com"
githubUrl: "https://github.com/you/project"
featured: true
publishDate: 2024-06-05T10:00:00.000Z
status: "completed"
---

# Your Project

Detailed description of your project...
```

**Blog Posts**: `src/content/blog/2024-06-05-your-post.md`
```markdown
---
title: "Your Blog Post Title"
description: "Brief description"
publishDate: 2024-06-05T10:00:00.000Z
author: "Your Name"
tags: ["Web Development", "Tutorial"]
featured: false
draft: false
---

# Your Blog Post

Your blog post content...
```

## 🚀 Deployment

### Vercel (Recommended)

1. **Push to GitHub**:
```bash
git add .
git commit -m "Initial commit"
git push origin main
```

2. **Deploy to Vercel**:
   - Go to [vercel.com](https://vercel.com)
   - Import your GitHub repository
   - Vercel will automatically detect Astro and configure build settings
   - Deploy!

3. **Configure Domain** (optional):
   - Add your custom domain in Vercel dashboard
   - Update `site` URL in `astro.config.mjs`

### Other Platforms

**Netlify**:
- Connect GitHub repository
- Build command: `npm run build`
- Publish directory: `dist`

**GitHub Pages**:
- Enable GitHub Pages in repository settings
- Use GitHub Actions for automated deployment

**Cloudflare Pages**:
- Connect GitHub repository
- Build command: `npm run build`
- Build output directory: `dist`

## ⚙️ Configuration

### Site Configuration

Edit `astro.config.mjs`:
```javascript
export default defineConfig({
  site: 'https://your-domain.com', // Update with your domain
  // ... other config
});
```

### CMS Configuration

Edit `public/admin/config.yml`:
- Update backend settings for your Git provider
- Modify collections and fields as needed
- Configure media folder paths

### Styling Customization

Edit CSS custom properties in `src/layouts/Layout.astro`:
```css
:root {
  --primary-color: #2563eb;    /* Your brand color */
  --secondary-color: #64748b;  /* Secondary text color */
  --text-color: #1e293b;       /* Main text color */
  --bg-color: #ffffff;         /* Background color */
  --border-color: #e2e8f0;     /* Border color */
  --hover-color: #f8fafc;      /* Hover states */
}
```

## 🔧 Customization

### Adding New Content Types

1. **Update Content Schema** (`src/content/config.ts`):
```typescript
const newCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    // ... other fields
  }),
});

export const collections = {
  'projects': projectsCollection,
  'blog': blogCollection,
  'new-collection': newCollection,
};
```

2. **Update CMS Config** (`public/admin/config.yml`):
```yaml
collections:
  - name: "new-collection"
    label: "New Collection"
    folder: "src/content/new-collection"
    create: true
    fields:
      - { label: "Title", name: "title", widget: "string" }
      # ... other fields
```

3. **Create Pages** for the new content type

### Adding Components

Create new components in `src/components/` and import them in your pages:
```astro
---
import YourComponent from '../components/YourComponent.astro';
---

<YourComponent />
```

## 📝 Content Guidelines

### Images
- Place images in `public/images/`
- Use descriptive filenames
- Optimize images for web (WebP format recommended)
- Include alt text for accessibility

### SEO
- Write descriptive titles and descriptions
- Use proper heading hierarchy (H1, H2, H3)
- Include relevant keywords naturally
- Add meta descriptions to all pages

### Performance
- Keep images optimized and properly sized
- Use lazy loading for images
- Minimize custom JavaScript
- Leverage Astro's static generation

## 🐛 Troubleshooting

### Common Issues

**Build Errors**:
- Check content frontmatter matches schema
- Ensure all required fields are present
- Validate date formats (ISO 8601)

**CMS Access Issues**:
- Verify authentication setup
- Check network connectivity
- Ensure proper permissions

**Styling Issues**:
- Check CSS custom properties
- Verify component imports
- Use browser dev tools for debugging

### Getting Help

- Check [Astro Documentation](https://docs.astro.build)
- Review [Decap CMS Documentation](https://decapcms.org/docs/)
- Open an issue in the repository

## 📞 Support

For questions or issues:
- Create an issue in the GitHub repository
- Check the documentation links above
- Review the code comments for guidance

---

Happy coding! 🚀
